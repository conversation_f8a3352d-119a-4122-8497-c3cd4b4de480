Noto Sans Variable Font
=======================

This download contains Noto Sans as both variable fonts and static fonts.

Noto Sans is a variable font with these axes:
  wdth
  wght

This means all the styles are contained in these files:
  NotoSans-VariableFont_wdth,wght.ttf
  NotoSans-Italic-VariableFont_wdth,wght.ttf

If your app fully supports variable fonts, you can now pick intermediate styles
that aren’t available as static fonts. Not all apps support variable fonts, and
in those cases you can use the static font files for Noto Sans:
  static/NotoSans_ExtraCondensed-Thin.ttf
  static/NotoSans_ExtraCondensed-ExtraLight.ttf
  static/NotoSans_ExtraCondensed-Light.ttf
  static/NotoSans_ExtraCondensed-Regular.ttf
  static/NotoSans_ExtraCondensed-Medium.ttf
  static/NotoSans_ExtraCondensed-SemiBold.ttf
  static/NotoSans_ExtraCondensed-Bold.ttf
  static/NotoSans_ExtraCondensed-ExtraBold.ttf
  static/NotoSans_ExtraCondensed-Black.ttf
  static/NotoSans_Condensed-Thin.ttf
  static/NotoSans_Condensed-ExtraLight.ttf
  static/NotoSans_Condensed-Light.ttf
  static/NotoSans_Condensed-Regular.ttf
  static/NotoSans_Condensed-Medium.ttf
  static/NotoSans_Condensed-SemiBold.ttf
  static/NotoSans_Condensed-Bold.ttf
  static/NotoSans_Condensed-ExtraBold.ttf
  static/NotoSans_Condensed-Black.ttf
  static/NotoSans_SemiCondensed-Thin.ttf
  static/NotoSans_SemiCondensed-ExtraLight.ttf
  static/NotoSans_SemiCondensed-Light.ttf
  static/NotoSans_SemiCondensed-Regular.ttf
  static/NotoSans_SemiCondensed-Medium.ttf
  static/NotoSans_SemiCondensed-SemiBold.ttf
  static/NotoSans_SemiCondensed-Bold.ttf
  static/NotoSans_SemiCondensed-ExtraBold.ttf
  static/NotoSans_SemiCondensed-Black.ttf
  static/NotoSans-Thin.ttf
  static/NotoSans-ExtraLight.ttf
  static/NotoSans-Light.ttf
  static/NotoSans-Regular.ttf
  static/NotoSans-Medium.ttf
  static/NotoSans-SemiBold.ttf
  static/NotoSans-Bold.ttf
  static/NotoSans-ExtraBold.ttf
  static/NotoSans-Black.ttf
  static/NotoSans_ExtraCondensed-ThinItalic.ttf
  static/NotoSans_ExtraCondensed-ExtraLightItalic.ttf
  static/NotoSans_ExtraCondensed-LightItalic.ttf
  static/NotoSans_ExtraCondensed-Italic.ttf
  static/NotoSans_ExtraCondensed-MediumItalic.ttf
  static/NotoSans_ExtraCondensed-SemiBoldItalic.ttf
  static/NotoSans_ExtraCondensed-BoldItalic.ttf
  static/NotoSans_ExtraCondensed-ExtraBoldItalic.ttf
  static/NotoSans_ExtraCondensed-BlackItalic.ttf
  static/NotoSans_Condensed-ThinItalic.ttf
  static/NotoSans_Condensed-ExtraLightItalic.ttf
  static/NotoSans_Condensed-LightItalic.ttf
  static/NotoSans_Condensed-Italic.ttf
  static/NotoSans_Condensed-MediumItalic.ttf
  static/NotoSans_Condensed-SemiBoldItalic.ttf
  static/NotoSans_Condensed-BoldItalic.ttf
  static/NotoSans_Condensed-ExtraBoldItalic.ttf
  static/NotoSans_Condensed-BlackItalic.ttf
  static/NotoSans_SemiCondensed-ThinItalic.ttf
  static/NotoSans_SemiCondensed-ExtraLightItalic.ttf
  static/NotoSans_SemiCondensed-LightItalic.ttf
  static/NotoSans_SemiCondensed-Italic.ttf
  static/NotoSans_SemiCondensed-MediumItalic.ttf
  static/NotoSans_SemiCondensed-SemiBoldItalic.ttf
  static/NotoSans_SemiCondensed-BoldItalic.ttf
  static/NotoSans_SemiCondensed-ExtraBoldItalic.ttf
  static/NotoSans_SemiCondensed-BlackItalic.ttf
  static/NotoSans-ThinItalic.ttf
  static/NotoSans-ExtraLightItalic.ttf
  static/NotoSans-LightItalic.ttf
  static/NotoSans-Italic.ttf
  static/NotoSans-MediumItalic.ttf
  static/NotoSans-SemiBoldItalic.ttf
  static/NotoSans-BoldItalic.ttf
  static/NotoSans-ExtraBoldItalic.ttf
  static/NotoSans-BlackItalic.ttf

Get started
-----------

1. Install the font files you want to use

2. Use your app's font picker to view the font family and all the
available styles

Learn more about variable fonts
-------------------------------

  https://developers.google.com/web/fundamentals/design-and-ux/typography/variable-fonts
  https://variablefonts.typenetwork.com
  https://medium.com/variable-fonts

In desktop apps

  https://theblog.adobe.com/can-variable-fonts-illustrator-cc
  https://helpx.adobe.com/nz/photoshop/using/fonts.html#variable_fonts

Online

  https://developers.google.com/fonts/docs/getting_started
  https://developer.mozilla.org/en-US/docs/Web/CSS/CSS_Fonts/Variable_Fonts_Guide
  https://developer.microsoft.com/en-us/microsoft-edge/testdrive/demos/variable-fonts

Installing fonts

  MacOS: https://support.apple.com/en-us/HT201749
  Linux: https://www.google.com/search?q=how+to+install+a+font+on+gnu%2Blinux
  Windows: https://support.microsoft.com/en-us/help/314960/how-to-install-or-remove-a-font-in-windows

Android Apps

  https://developers.google.com/fonts/docs/android
  https://developer.android.com/guide/topics/ui/look-and-feel/downloadable-fonts

License
-------
Please read the full license text (OFL.txt) to understand the permissions,
restrictions and requirements for usage, redistribution, and modification.

You can use them in your products & projects – print or digital,
commercial or otherwise.

This isn't legal advice, please consider consulting a lawyer and see the full
license for all details.
