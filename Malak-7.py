""" FINAL PROJECT"""

# Imports
import pygame
import glob
import time
import os
import json

# Initialize Pygame
pygame.init()

# Set up the display
SCREEN_WIDTH, SCREEN_HEIGHT = 800, 600
screen = pygame.display.set_mode((SCREEN_WIDTH, SCREEN_HEIGHT))
pygame.display.set_caption("My game")

clock = pygame.time.Clock()

# Define colors
BLACK = (0, 0, 0)
WHITE = (255, 255, 255)
RED = (255, 0, 0)
GREEN = (0, 255, 0)
DARK_GREEN = (0,51,25)
BLUE = (0, 0, 255)
PINK = (255,192,203)
YELLOW = (255,255,51)

# Variables
font = pygame.font.Font(None, 36)
# game states
MENU = 0
GAME = 1
PAUSE = 2
NOT_YET = 3
LOAD = 4
game_state= MENU   
current_world = "kevin"


# Player class
class Player:
    def __init__(self, x, y, color, size, controls):
        self.x = x
        self.y = y
        self.color = color
        self.width, self.height = size         
        self.controls = controls
        self.is_alive = True
        self.speed = 5
        self.y_velocity = 0 # Tracks falling speed
        self.on_ground = False

# Instances of the player class
kevin = Player(500, 500, YELLOW, (50, 50), (pygame.K_LEFT, pygame.K_RIGHT, pygame.K_DOWN))
zeth = Player(100, 100, GREEN, (50, 50), (pygame.K_a, pygame.K_d, pygame.K_w))

# What I will have on the menu
menu_options = [
                    "NEW",
                    "LOAD",
                    "SETTINGS",
                    "CHARACTERS",
                    "LANGUAGE",
                    "ACHIEVEMENTS",
                    "GAMEPLAY",
                    "CREDITS",
                    "CONTROLS",
                    "HELP",
                    "EXIT"
                ]
selected_option = 0
options_rects = []


# save function
load_selected = 0
saved_files = []
saved_rects = []








RUNNING = True
while RUNNING:
    # Handle events

    pressed_keys = pygame.key.get_pressed()
    mouse_pos = pygame.mouse.get_pos()

    for event in pygame.event.get():
        if event.type == pygame.QUIT:
            RUNNING = False # quit

        if event.type == pygame.KEYDOWN:
            if event.key == pygame.K_ESCAPE and game_state == GAME:
                game_state = PAUSE # Pause
         
            elif event.key == pygame.K_b and game_state == PAUSE:
                game_state = GAME

            elif event.key == pygame.K_q and game_state == PAUSE:
                RUNNING = False

            elif event.key == pygame.K_x and game_state == GAME:
                current_world = "kevin"

            elif event.key == pygame.K_y and game_state == GAME:
                current_world = "zeth"

            elif event.key == pygame.K_p and game_state == NOT_YET:
                    game_state = MENU

            elif event.key == pygame.K_UP and game_state == MENU:
                selected_option = (selected_option - 1) % len(menu_options)

            elif event.key == pygame.K_DOWN and game_state == MENU:
                selected_option = (selected_option + 1) % len(menu_options)
            
            if event.key == pygame.K_UP and game_state == LOAD:
                load_selected = (load_selected - 1) % len(saved_files) if saved_files else 0

            elif event.key == pygame.K_DOWN and game_state == LOAD:
                load_selected = (load_selected + 1) % len(saved_files) if saved_files else 0

            elif event.key == pygame.K_RETURN and game_state == LOAD:
                if saved_files:
                    selected_save = saved_files[load_selected]
                    try:
                                    with open(selected_save, "r") as f:
                                        game_data = json.load(f)
                                        kevin.x = game_data["kevin_x"]
                                        kevin.y = game_data["kevin_y"]
                                        zeth.x = game_data["zeth_x"]
                                        zeth.y = game_data["zeth_y"]
                                        current_world = game_data["current_world"]
                                        game_state = GAME
                    except Exception as e:
                                    game_state = NOT_YET
            
            elif event.key == pygame.K_b and game_state == LOAD:
                 game_state = MENU

            elif event.key == pygame.K_RETURN:
                selected = menu_options[selected_option]
                if selected == "NEW":
                    game_state = GAME

                elif selected == "EXIT":
                    RUNNING = False

                elif selected == "LOAD":
                    game_state = LOAD

                else:
                    game_state = NOT_YET


        if event.type == pygame.MOUSEBUTTONDOWN and event.button == 1:  # Left-click
                if game_state == MENU:

                    for i, rect in enumerate(options_rects):
                        if rect.collidepoint(event.pos):
                            selected_option = i
                            selected = menu_options[selected_option]

                            if selected == "NEW":
                                game_state = GAME

                            elif selected == "EXIT":
                                RUNNING = False

                            elif selected == "LOAD":
                                game_state = LOAD

                            else:
                                game_state = NOT_YET

                elif game_state == LOAD:

                     for i, rect in enumerate(saved_rects):
                        if rect.collidepoint(event.pos):
                            load_selected= i
                            selected_save = saved_files[load_selected]

                            if selected == "BACK":
                               game_state = MENU
                            else:
                              try:
                                    with open(selected_save, "r") as f:
                                        game_data = json.load(f)
                                        kevin.x = game_data["kevin_x"]
                                        kevin.y = game_data["kevin_y"]
                                        zeth.x = game_data["zeth_x"]
                                        zeth.y = game_data["zeth_y"]
                                        current_world = game_data["current_world"]
                                        game_state = GAME
                              except Exception as e:
                                    game_state = NOT_YET

                        

                



        

# Handling game states
    if game_state == MENU: # menu logic
        screen.fill(PINK)
        title_text = font.render("GAME", True, WHITE)
        title_rect = title_text.get_rect(center=(SCREEN_WIDTH // 2, SCREEN_HEIGHT // 8))
        screen.blit(title_text, title_rect)
        options_rects.clear()

        for i, option in enumerate(menu_options):
            text = font.render(option, True, BLACK)  # Temporary color
            rect = text.get_rect(center=(SCREEN_WIDTH // 2, SCREEN_HEIGHT // 4 + i * 40))
            options_rects.append(rect)

        for i, option in enumerate (menu_options):
            hover = options_rects[i].collidepoint(mouse_pos) if i < len(options_rects) else False
            if hover:
                text = font.render(option, True,BLUE)
            elif i == selected_option:
                text = font.render(option, True, RED)
            else:
                text = font.render(option, True, BLACK)
            rect = text.get_rect(center=(SCREEN_WIDTH // 2, SCREEN_HEIGHT //4 +i*40))
            screen.blit(text, rect)
            
    elif game_state ==  NOT_YET:
        screen.fill(YELLOW)
        title_text = font.render("NOT IMPELEMENTED", True, RED)
        title_rect = title_text.get_rect(center=(SCREEN_WIDTH // 2, SCREEN_HEIGHT // 2))
        screen.blit(title_text, title_rect)

        

    elif game_state == LOAD:
        screen.fill(PINK)
        title_text = font.render("LOAD GAME", True, WHITE)
        title_rect = title_text.get_rect(center=(SCREEN_WIDTH // 2, SCREEN_HEIGHT // 8))
        screen.blit(title_text, title_rect)

        for i, saved_file in enumerate (saved_files):
            hover = saved_rects[i].collidepoint(mouse_pos) if i < len(saved_rects) else False
            if hover:
                text = font.render(saved_file, True,BLUE)
                saved_files.sort(key=os.path.getmtime, reverse=True) 

                for i, saved_file in enumerate(saved_files):

                    file_name = os.path.basename(saved_file)
                    mod_time = time.strftime("%Y-%m-%d %H:%M", time.localtime(os.path.getmtime(saved_file)))
                    display_text = f"{file_name} ({mod_time})"
                    text = font.render(display_text, True, BLACK) # Default color for now
                    rect = text.get_rect(center=(SCREEN_WIDTH // 2, SCREEN_HEIGHT // 4 + i * 40))
                    screen.blit(text, rect)

                    saved_rects.append(rect)

            elif i == selected_option:
                text = font.render(saved_file, True, RED)

            elif not saved_files:
                no_saves_text = font.render("No saved games found!", True, RED)
                no_saves_rect = no_saves_text.get_rect(center=(SCREEN_WIDTH // 2, SCREEN_HEIGHT // 2))
                screen.blit(no_saves_text, no_saves_rect)

            else:
                text = font.render(saved_file, True, BLACK)
            rect = text.get_rect(center=(SCREEN_WIDTH // 2, SCREEN_HEIGHT //4 +i*40))
            screen.blit(text, rect)

        saved_files = glob.glob("saves/*.json") 
        saved_rects.clear()

        

       
            
                    

    elif game_state == GAME: # game play logic
        if current_world == "kevin":
            screen.fill(PINK)
            pygame.draw.rect(screen, kevin.color, (kevin.x, kevin.y, kevin.width, kevin.height))
        elif current_world == "zeth":
            screen.fill(DARK_GREEN)
            pygame.draw.rect(screen, zeth.color, (zeth.x, zeth.y, zeth.width, zeth.height))

        if pressed_keys[kevin.controls[0]] and kevin.x > 0:
            kevin.x -= kevin.speed # Left
        if pressed_keys[kevin.controls[1]] and kevin.x < SCREEN_WIDTH - kevin.width:
            kevin.x += kevin.speed # Right
        if pressed_keys[kevin.controls[2]] and kevin.on_ground:
            kevin.y_velocity = 10
            kevin.on_ground = False
        

        if pressed_keys[zeth.controls[0]] and zeth.x > 0:
            zeth.x -= zeth.speed # Left
        if pressed_keys[zeth.controls[1]] and zeth.x < SCREEN_WIDTH - zeth.width:
              zeth.x += zeth.speed  # Right
        if pressed_keys[zeth.controls[2]] and zeth.on_ground:
            zeth.y_velocity = -10
            zeth.on_ground = False



        kevin.y_velocity -= 0.5
        kevin.y += kevin.y_velocity
        kevin.on_ground = False
        if kevin.y < 0:
            kevin.y = 0
            kevin.y_velocity = 0
            kevin.on_ground = True

        zeth.y_velocity += 0.5
        zeth.y += zeth.y_velocity
        zeth.on_ground = False
        if zeth.y > SCREEN_HEIGHT - zeth.height:
            zeth.y = SCREEN_HEIGHT - zeth.height
            zeth.y_velocity = 0
            zeth.on_ground = True

    elif game_state == PAUSE: # pause logic
        text = font.render("Paused: B to Resume, Q to Quit", True, WHITE)
        text_rect = text.get_rect(center=(SCREEN_WIDTH // 2, SCREEN_HEIGHT // 2))
        if current_world == "kevin":
            screen.fill(PINK)
            pygame.draw.rect(screen, kevin.color, (kevin.x, kevin.y, kevin.width, kevin.height))
        elif current_world == "zeth":
            screen.fill(DARK_GREEN)
            pygame.draw.rect(screen, zeth.color, (zeth.x, zeth.y, zeth.width, zeth.height))

        screen.blit(text, text_rect)
                
  


    

    pygame.display.flip()
    clock.tick(60)

pygame.quit()


