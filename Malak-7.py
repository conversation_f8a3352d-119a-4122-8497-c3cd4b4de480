""" THE DIMENSIONAL RIFT GUARDIANS - FINAL PROJECT"""

# Imports
import pygame
import glob
import time
import os
import json
import random
import math

# Initialize Pygame
pygame.init()

# Set up the display
SCREEN_WIDTH, SCREEN_HEIGHT = 800, 600
screen = pygame.display.set_mode((SCREEN_WIDTH, SCREEN_HEIGHT))
pygame.display.set_caption("The Dimensional Rift Guardians")

clock = pygame.time.Clock()

# Define colors
BLACK = (0, 0, 0)
WHITE = (255, 255, 255)
RED = (255, 0, 0)
GREEN = (0, 255, 0)
DARK_GREEN = (0,51,25)
BLUE = (0, 0, 255)
PINK = (255,192,203)
YELLOW = (255,255,51)
PURPLE = (128, 0, 128)
ORANGE = (255, 165, 0)
CYAN = (0, 255, 255)
MAGENTA = (255, 0, 255)
GRAY = (128, 128, 128)
DARK_BLUE = (0, 0, 139)
LIGHT_BLUE = (173, 216, 230)
GOLD = (255, 215, 0)

# Variables
font = pygame.font.Font(None, 36)
small_font = pygame.font.Font(None, 24)
large_font = pygame.font.Font(None, 48)

# game states
MENU = 0
GAME = 1
PAUSE = 2
NOT_YET = 3
LOAD = 4
GAME_OVER = 5
VICTORY = 6
STORY = 7
game_state = MENU
current_world = "kevin"

# Game progression variables
game_level = 1
max_levels = 5
crystals_collected = {"kevin": 0, "zeth": 0}
total_crystals_needed = 15
dimensional_stability = 100.0  # Decreases over time
game_timer = 0
shadow_entities_defeated = 0
puzzles_solved = 0
camera_offset = {"kevin": 0, "zeth": 0}


# Player class
class Player:
    def __init__(self, x, y, color, size, controls):
        self.x = x
        self.y = y
        self.color = color
        self.width, self.height = size
        self.controls = controls
        self.is_alive = True
        self.speed = 5
        self.y_velocity = 0 # Tracks falling speed
        self.on_ground = False
        self.health = 100
        self.max_health = 100
        self.energy = 100
        self.max_energy = 100
        self.has_crystal_power = False
        self.invulnerable_timer = 0
        self.crystals_held = 0

# Crystal Fragment class
class Crystal:
    def __init__(self, x, y, crystal_type="normal"):
        self.x = x
        self.y = y
        self.width = 20
        self.height = 20
        self.collected = False
        self.crystal_type = crystal_type  # normal, power, echo
        self.pulse_timer = 0
        self.color = CYAN if crystal_type == "normal" else GOLD if crystal_type == "power" else MAGENTA

    def update(self):
        self.pulse_timer += 0.1

    def draw(self, screen, camera_offset):
        if not self.collected:
            # Pulsing effect
            pulse = abs(math.sin(self.pulse_timer)) * 10
            size = self.width + pulse
            pygame.draw.rect(screen, self.color,
                           (self.x + camera_offset - pulse//2, self.y - pulse//2, size, size))
            # Inner glow
            pygame.draw.rect(screen, WHITE,
                           (self.x + camera_offset + 5, self.y + 5, self.width - 10, self.height - 10))

# Shadow Entity class
class ShadowEntity:
    def __init__(self, x, y, entity_type="basic"):
        self.x = x
        self.y = y
        self.width = 40
        self.height = 40
        self.health = 50 if entity_type == "basic" else 100
        self.max_health = self.health
        self.speed = 2 if entity_type == "basic" else 1
        self.entity_type = entity_type
        self.direction = random.choice([-1, 1])
        self.attack_timer = 0
        self.is_alive = True
        self.color = PURPLE if entity_type == "basic" else DARK_BLUE

    def update(self, kevin, zeth, current_world):
        if not self.is_alive:
            return

        # Move towards player
        player = kevin if current_world == "kevin" else zeth
        if player.x < self.x:
            self.x -= self.speed
        elif player.x > self.x:
            self.x += self.speed

        self.attack_timer += 1

    def draw(self, screen, camera_offset):
        if self.is_alive:
            # Draw shadow entity
            pygame.draw.rect(screen, self.color,
                           (self.x + camera_offset, self.y, self.width, self.height))
            # Health bar
            if self.health < self.max_health:
                bar_width = 40
                bar_height = 5
                health_ratio = self.health / self.max_health
                pygame.draw.rect(screen, RED,
                               (self.x + camera_offset, self.y - 10, bar_width, bar_height))
                pygame.draw.rect(screen, GREEN,
                               (self.x + camera_offset, self.y - 10, bar_width * health_ratio, bar_height))

# Instances of the player class
kevin = Player(100, 400, YELLOW, (50, 50), (pygame.K_LEFT, pygame.K_RIGHT, pygame.K_DOWN))
zeth = Player(100, 400, GREEN, (50, 50), (pygame.K_a, pygame.K_d, pygame.K_w))

# Platform class for dimensional echoes
class Platform:
    def __init__(self, x, y, width, height, platform_type="normal"):
        self.x = x
        self.y = y
        self.width = width
        self.height = height
        self.platform_type = platform_type  # normal, echo, puzzle
        self.active = True
        self.echo_timer = 0
        self.color = GRAY if platform_type == "normal" else LIGHT_BLUE if platform_type == "echo" else ORANGE

    def update(self):
        if self.platform_type == "echo":
            self.echo_timer += 0.1
            # Echo platforms phase in and out
            self.active = math.sin(self.echo_timer) > 0

    def draw(self, screen, camera_offset):
        if self.active:
            alpha = 255 if self.platform_type != "echo" else int(abs(math.sin(self.echo_timer)) * 255)
            color = (*self.color, alpha) if self.platform_type == "echo" else self.color
            pygame.draw.rect(screen, self.color,
                           (self.x + camera_offset, self.y, self.width, self.height))

# Game objects initialization
def initialize_level(level):
    global crystals, shadow_entities, platforms, dimensional_stability

    crystals = []
    shadow_entities = []
    platforms = []
    dimensional_stability = 100.0

    # Level 1: Tutorial
    if level == 1:
        # Kevin's world crystals
        crystals.extend([
            Crystal(300, 350),
            Crystal(500, 200),
            Crystal(700, 400)
        ])

        # Zeth's world crystals
        crystals.extend([
            Crystal(400, 300),
            Crystal(600, 150),
            Crystal(800, 350)
        ])

        # Basic platforms
        platforms.extend([
            Platform(200, 450, 100, 20),
            Platform(400, 350, 100, 20),
            Platform(600, 250, 100, 20),
            Platform(800, 450, 100, 20)
        ])

        # Few shadow entities
        shadow_entities.extend([
            ShadowEntity(350, 400),
            ShadowEntity(650, 200)
        ])

# What I will have on the menu
menu_options = [
                    "NEW GAME",
                    "LOAD",
                    "CONTROLS",
                    "HELP",
                    "EXIT"
                ]
selected_option = 0
options_rects = []

# save function
load_selected = 0
saved_files = []
saved_rects = []

# Initialize game objects
crystals = []
shadow_entities = []
platforms = []








# Game functions
def update_game():
    global dimensional_stability, game_timer, game_state

    # Update game timer
    game_timer += 1

    # Decrease dimensional stability over time
    dimensional_stability -= 0.02

    # Update crystals
    for crystal in crystals:
        crystal.update()

    # Update shadow entities
    for entity in shadow_entities:
        entity.update(kevin, zeth, current_world)

    # Update platforms
    for platform in platforms:
        platform.update()

    # Check win/lose conditions
    if dimensional_stability <= 0:
        game_state = GAME_OVER
    elif crystals_collected["kevin"] + crystals_collected["zeth"] >= total_crystals_needed:
        if game_level < max_levels:
            next_level()
        else:
            game_state = VICTORY

def next_level():
    global game_level
    game_level += 1
    initialize_level(game_level)
    kevin.x, kevin.y = 100, 400
    zeth.x, zeth.y = 100, 400

def check_collisions():
    global crystals_collected, shadow_entities_defeated

    # Current player
    player = kevin if current_world == "kevin" else zeth
    player_rect = pygame.Rect(player.x, player.y, player.width, player.height)

    # Crystal collection
    for crystal in crystals:
        if not crystal.collected:
            crystal_rect = pygame.Rect(crystal.x, crystal.y, crystal.width, crystal.height)
            if player_rect.colliderect(crystal_rect):
                crystal.collected = True
                crystals_collected[current_world] += 1
                if crystal.crystal_type == "power":
                    player.has_crystal_power = True
                    player.energy = player.max_energy

    # Shadow entity combat
    for entity in shadow_entities:
        if entity.is_alive:
            entity_rect = pygame.Rect(entity.x, entity.y, entity.width, entity.height)
            if player_rect.colliderect(entity_rect):
                if player.has_crystal_power and player.energy > 20:
                    entity.health -= 25
                    player.energy -= 20
                    if entity.health <= 0:
                        entity.is_alive = False
                        shadow_entities_defeated += 1
                elif player.invulnerable_timer <= 0:
                    player.health -= 10
                    player.invulnerable_timer = 60  # 1 second of invulnerability

    # Platform collisions
    for platform in platforms:
        if platform.active:
            platform_rect = pygame.Rect(platform.x, platform.y, platform.width, platform.height)
            if player_rect.colliderect(platform_rect) and player.y_velocity <= 0:
                player.y = platform.y - player.height
                player.y_velocity = 0
                player.on_ground = True

RUNNING = True
while RUNNING:
    # Handle events
    pressed_keys = pygame.key.get_pressed()
    mouse_pos = pygame.mouse.get_pos()

    for event in pygame.event.get():
        if event.type == pygame.QUIT:
            RUNNING = False # quit

        if event.type == pygame.KEYDOWN:
            if event.key == pygame.K_ESCAPE and game_state == GAME:
                game_state = PAUSE # Pause

            elif event.key == pygame.K_r and game_state == PAUSE:
                game_state = GAME

            elif event.key == pygame.K_q and (game_state == PAUSE or game_state == GAME_OVER):
                RUNNING = False

            elif event.key == pygame.K_x and game_state == GAME:
                current_world = "kevin"

            elif event.key == pygame.K_z and game_state == GAME:
                current_world = "zeth"

            elif event.key == pygame.K_SPACE and game_state == GAME:
                # Crystal power attack
                player = kevin if current_world == "kevin" else zeth
                if player.has_crystal_power and player.energy > 30:
                    player.energy -= 30
                    # Damage nearby enemies
                    for entity in shadow_entities:
                        if entity.is_alive:
                            distance = abs(entity.x - player.x) + abs(entity.y - player.y)
                            if distance < 100:
                                entity.health -= 50

            elif event.key == pygame.K_p and game_state == NOT_YET:
                game_state = MENU

            elif event.key == pygame.K_UP and game_state == MENU:
                selected_option = (selected_option - 1) % len(menu_options)

            elif event.key == pygame.K_DOWN and game_state == MENU:
                selected_option = (selected_option + 1) % len(menu_options)
            elif event.key == pygame.K_RETURN and game_state == STORY:
                game_state = GAME
                initialize_level(1)

            elif event.key == pygame.K_RETURN and (game_state == GAME_OVER or game_state == VICTORY):
                # Restart game
                global game_level, crystals_collected, dimensional_stability, game_timer, shadow_entities_defeated
                game_level = 1
                crystals_collected = {"kevin": 0, "zeth": 0}
                dimensional_stability = 100.0
                game_timer = 0
                shadow_entities_defeated = 0
                kevin.health = kevin.max_health
                zeth.health = zeth.max_health
                kevin.energy = kevin.max_energy
                zeth.energy = zeth.max_energy
                kevin.has_crystal_power = False
                zeth.has_crystal_power = False
                game_state = MENU

            elif event.key == pygame.K_RETURN and game_state == MENU:
                selected = menu_options[selected_option]
                if selected == "NEW GAME":
                    game_state = STORY

                elif selected == "EXIT":
                    RUNNING = False

                elif selected == "LOAD":
                    game_state = LOAD

                elif selected == "CONTROLS":
                    game_state = NOT_YET

                elif selected == "HELP":
                    game_state = NOT_YET

                else:
                    game_state = NOT_YET


        if event.type == pygame.MOUSEBUTTONDOWN and event.button == 1:  # Left-click
                if game_state == MENU:

                    for i, rect in enumerate(options_rects):
                        if rect.collidepoint(event.pos):
                            selected_option = i
                            selected = menu_options[selected_option]

                            if selected == "NEW":
                                game_state = GAME

                            elif selected == "EXIT":
                                RUNNING = False

                            elif selected == "LOAD":
                                game_state = LOAD

                            else:
                                game_state = NOT_YET

                elif game_state == LOAD:

                     for i, rect in enumerate(saved_rects):
                        if rect.collidepoint(event.pos):
                            load_selected= i
                            selected_save = saved_files[load_selected]

                            if selected == "BACK":
                               game_state = MENU
                            else:
                              try:
                                    with open(selected_save, "r") as f:
                                        game_data = json.load(f)
                                        kevin.x = game_data["kevin_x"]
                                        kevin.y = game_data["kevin_y"]
                                        zeth.x = game_data["zeth_x"]
                                        zeth.y = game_data["zeth_y"]
                                        current_world = game_data["current_world"]
                                        game_state = GAME
                              except Exception as e:
                                    game_state = NOT_YET

                        

                



        

# Handling game states
    if game_state == MENU: # menu logic
        screen.fill(PINK)
        title_text = font.render("GAME", True, WHITE)
        title_rect = title_text.get_rect(center=(SCREEN_WIDTH // 2, SCREEN_HEIGHT // 8))
        screen.blit(title_text, title_rect)
        options_rects.clear()

        for i, option in enumerate(menu_options):
            text = font.render(option, True, BLACK)  # Temporary color
            rect = text.get_rect(center=(SCREEN_WIDTH // 2, SCREEN_HEIGHT // 4 + i * 40))
            options_rects.append(rect)

        for i, option in enumerate (menu_options):
            hover = options_rects[i].collidepoint(mouse_pos) if i < len(options_rects) else False
            if hover:
                text = font.render(option, True,BLUE)
            elif i == selected_option:
                text = font.render(option, True, RED)
            else:
                text = font.render(option, True, BLACK)
            rect = text.get_rect(center=(SCREEN_WIDTH // 2, SCREEN_HEIGHT //4 +i*40))
            screen.blit(text, rect)
            
    elif game_state ==  NOT_YET:
        screen.fill(YELLOW)
        title_text = font.render("NOT IMPELEMENTED", True, RED)
        title_rect = title_text.get_rect(center=(SCREEN_WIDTH // 2, SCREEN_HEIGHT // 2))
        screen.blit(title_text, title_rect)

        

    elif game_state == LOAD:
        screen.fill(PINK)
        title_text = font.render("LOAD GAME", True, WHITE)
        title_rect = title_text.get_rect(center=(SCREEN_WIDTH // 2, SCREEN_HEIGHT // 8))
        screen.blit(title_text, title_rect)

        for i, saved_file in enumerate (saved_files):
            hover = saved_rects[i].collidepoint(mouse_pos) if i < len(saved_rects) else False
            if hover:
                text = font.render(saved_file, True,BLUE)
                saved_files.sort(key=os.path.getmtime, reverse=True) 

                for i, saved_file in enumerate(saved_files):

                    file_name = os.path.basename(saved_file)
                    mod_time = time.strftime("%Y-%m-%d %H:%M", time.localtime(os.path.getmtime(saved_file)))
                    display_text = f"{file_name} ({mod_time})"
                    text = font.render(display_text, True, BLACK) # Default color for now
                    rect = text.get_rect(center=(SCREEN_WIDTH // 2, SCREEN_HEIGHT // 4 + i * 40))
                    screen.blit(text, rect)

                    saved_rects.append(rect)

            elif i == selected_option:
                text = font.render(saved_file, True, RED)

            elif not saved_files:
                no_saves_text = font.render("No saved games found!", True, RED)
                no_saves_rect = no_saves_text.get_rect(center=(SCREEN_WIDTH // 2, SCREEN_HEIGHT // 2))
                screen.blit(no_saves_text, no_saves_rect)

            else:
                text = font.render(saved_file, True, BLACK)
            rect = text.get_rect(center=(SCREEN_WIDTH // 2, SCREEN_HEIGHT //4 +i*40))
            screen.blit(text, rect)

        saved_files = glob.glob("saves/*.json") 
        saved_rects.clear()

        

       
            
                    

    elif game_state == GAME: # game play logic
        if current_world == "kevin":
            screen.fill(PINK)
            pygame.draw.rect(screen, kevin.color, (kevin.x, kevin.y, kevin.width, kevin.height))
        elif current_world == "zeth":
            screen.fill(DARK_GREEN)
            pygame.draw.rect(screen, zeth.color, (zeth.x, zeth.y, zeth.width, zeth.height))

        if pressed_keys[kevin.controls[0]] and kevin.x > 0:
            kevin.x -= kevin.speed # Left
        if pressed_keys[kevin.controls[1]] and kevin.x < SCREEN_WIDTH - kevin.width:
            kevin.x += kevin.speed # Right
        if pressed_keys[kevin.controls[2]] and kevin.on_ground:
            kevin.y_velocity = 10
            kevin.on_ground = False
        

        if pressed_keys[zeth.controls[0]] and zeth.x > 0:
            zeth.x -= zeth.speed # Left
        if pressed_keys[zeth.controls[1]] and zeth.x < SCREEN_WIDTH - zeth.width:
              zeth.x += zeth.speed  # Right
        if pressed_keys[zeth.controls[2]] and zeth.on_ground:
            zeth.y_velocity = -10
            zeth.on_ground = False



        kevin.y_velocity -= 0.5
        kevin.y += kevin.y_velocity
        kevin.on_ground = False
        if kevin.y < 0:
            kevin.y = 0
            kevin.y_velocity = 0
            kevin.on_ground = True

        zeth.y_velocity += 0.5
        zeth.y += zeth.y_velocity
        zeth.on_ground = False
        if zeth.y > SCREEN_HEIGHT - zeth.height:
            zeth.y = SCREEN_HEIGHT - zeth.height
            zeth.y_velocity = 0
            zeth.on_ground = True

    elif game_state == PAUSE: # pause logic
        text = font.render("Paused: B to Resume, Q to Quit", True, WHITE)
        text_rect = text.get_rect(center=(SCREEN_WIDTH // 2, SCREEN_HEIGHT // 2))
        if current_world == "kevin":
            screen.fill(PINK)
            pygame.draw.rect(screen, kevin.color, (kevin.x, kevin.y, kevin.width, kevin.height))
        elif current_world == "zeth":
            screen.fill(DARK_GREEN)
            pygame.draw.rect(screen, zeth.color, (zeth.x, zeth.y, zeth.width, zeth.height))

        screen.blit(text, text_rect)
                
  


    

    pygame.display.flip()
    clock.tick(60)

pygame.quit()


