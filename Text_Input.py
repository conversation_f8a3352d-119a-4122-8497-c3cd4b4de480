
""" FINAL PROJECT"""

# dont forget about language


# Imports
import pygame
import glob
import time
import os
import json
import gettext



#setting up gettext
local_direc = os.path.join(os.path.dirname(__file__), "locale")
current_language = "en"
#setting  up translations
translation = gettext.translation('messages', localedir = local_direc, languages=[current_language], fallback=True)
_ = translation.gettext

# Initialize Pygame
pygame.init()

#music
#music variables
music_on = False
volume = 0.5
settings_selected = 0

pygame.mixer.init()
pygame.mixer.music.load("suspense-gamevideo-track-339286 (1).mp3")
pygame.mixer.music.set_volume(volume)


# Set up the display
SCREEN_WIDTH, SCREEN_HEIGHT = 800, 600
screen = pygame.display.set_mode((SCREEN_WIDTH, SCREEN_HEIGHT))
pygame.display.set_caption("My game")
pygame.mixer.music.play(-1)

clock = pygame.time.Clock()

# Define colors
colors = {
"BLACK": (0, 0, 0),
"WHITE" : (255, 255, 255),
"RED" : (255, 0, 0),
"GREEN" : (0, 255, 0),
"DARK_GREEN" : (0,51,25),
"BLUE" : (0, 0, 255),
"PINK" : (255,192,203),
"YELLOW" : (255,255,51),
"PURPLE": (128, 0, 128),
"ORANGE": (255, 165, 0),
"CYAN": (0, 255, 255),
"MAGENTA": (255, 0, 255),
"GRAY": (128, 128, 128),
"DARK_BLUE": (0, 0, 139),
"LIGHT_BLUE": (173, 216, 230),
"GOLD": (255, 215, 0)
}
# Variables
script_path = os.path.abspath(__file__)
font = pygame.font.Font(None, 36)
small_font = pygame.font.Font(None, 24)
large_font = pygame.font.Font(None, 48)
# game states
MENU = 0
GAME = 1
PAUSE = 2
NOT_YET = 3
STORY = 11
LOAD = 4
CREDITS = 5
CONTROLS = 6
HELP = 7
SETTINGS = 8
LANGUAGE = 9
SCENE_1 = 10
INVENTORY = 12
GAME_OVER = 13
VICTORY = 14
previous_state = None
game_state= MENU
current_world = "kevin"

# Game progression variables
game_level = 1
max_levels = 5
crystals_collected = {"kevin": 0, "zeth": 0}
total_crystals_needed = 15
dimensional_stability = 100.0  # Decreases over time
game_timer = 0
shadow_entities_defeated = 0
puzzles_solved = 0
camera_offset = {"kevin": 0, "zeth": 0}

#a few saving variables
text_input = None
save_input_active = False

overwrite_confirm = False
overwrite_save_name = ""

delete_confirm = False
delete_save_name = ""

#used to go back to the menu 
back_rect = None

#brightness variables
brightness = 1.0
brightness_step = 0.1
brightness_min = 0.0
brightness_max = 1.0

slider_pos = 400
slider_width = 200
slider_height = 10



# Player class
class Player:
    def __init__(self, x, y, color, size, controls):
        self.x = x
        self.y = y
        self.color = color
        self.width, self.height = size
        self.controls = controls
        self.is_alive = True
        self.speed = 5
        self.y_velocity = 0 # Tracks falling speed
        self.on_ground = False
        self.health = 100
        self.max_health = 100
        self.energy = 100
        self.max_energy = 100
        self.has_crystal_power = False
        self.invulnerable_timer = 0

# Instances of the player class
kevin = Player(50, 100, colors["YELLOW"], (50, 50), (pygame.K_LEFT, pygame.K_RIGHT, pygame.K_DOWN))
zeth = Player(50, SCREEN_HEIGHT-150, colors["GREEN"], (50, 50), (pygame.K_a, pygame.K_d, pygame.K_w))

# Crystal Fragment class for the main gameplay
class Crystal:
    def __init__(self, x, y, crystal_type="normal", world="kevin"):
        self.x = x
        self.y = y
        self.width = 25
        self.height = 25
        self.crystal_type = crystal_type  # normal, power, echo
        self.world = world  # which world this crystal belongs to
        self.collected = False
        self.pulse_timer = 0
        self.color = colors["CYAN"] if crystal_type == "normal" else colors["GOLD"] if crystal_type == "power" else colors["MAGENTA"]

    def update(self):
        self.pulse_timer += 0.1

    def draw(self, screen, camera_offset):
        if not self.collected:
            # Pulsing effect
            import math
            pulse = abs(math.sin(self.pulse_timer)) * 8
            size = self.width + pulse
            pygame.draw.rect(screen, self.color,
                           (self.x + camera_offset - pulse//2, self.y - pulse//2, size, size))
            # Inner glow
            pygame.draw.rect(screen, colors["WHITE"],
                           (self.x + camera_offset + 8, self.y + 8, self.width - 16, self.height - 16))

# Shadow Entity class - the main enemies
class ShadowEntity:
    def __init__(self, x, y, entity_type="basic", world="kevin"):
        self.x = x
        self.y = y
        self.width = 45
        self.height = 45
        self.health = 60 if entity_type == "basic" else 120
        self.max_health = self.health
        self.speed = 1.5 if entity_type == "basic" else 1
        self.entity_type = entity_type
        self.world = world
        self.direction = 1
        self.attack_timer = 0
        self.is_alive = True
        self.color = colors["PURPLE"] if entity_type == "basic" else colors["DARK_BLUE"]
        self.patrol_start = x
        self.patrol_range = 150

    def update(self, kevin, zeth, current_world):
        if not self.is_alive or self.world != current_world:
            return

        # Simple AI: patrol and chase player when close
        player = kevin if current_world == "kevin" else zeth
        distance_to_player = abs(player.x - self.x)

        if distance_to_player < 100:  # Chase player
            if player.x < self.x:
                self.x -= self.speed
            elif player.x > self.x:
                self.x += self.speed
        else:  # Patrol
            self.x += self.direction * self.speed
            if abs(self.x - self.patrol_start) > self.patrol_range:
                self.direction *= -1

        self.attack_timer += 1

    def draw(self, screen, camera_offset):
        if self.is_alive:
            # Draw shadow entity with glowing effect
            pygame.draw.rect(screen, self.color,
                           (self.x + camera_offset, self.y, self.width, self.height))
            # Glowing border
            pygame.draw.rect(screen, colors["WHITE"],
                           (self.x + camera_offset, self.y, self.width, self.height), 2)
            # Health bar
            if self.health < self.max_health:
                bar_width = 45
                bar_height = 6
                health_ratio = self.health / self.max_health
                pygame.draw.rect(screen, colors["RED"],
                               (self.x + camera_offset, self.y - 12, bar_width, bar_height))
                pygame.draw.rect(screen, colors["GREEN"],
                               (self.x + camera_offset, self.y - 12, bar_width * health_ratio, bar_height))

# Enhanced GameObject for collectibles
class GameObject:
    def __init__(self, x, y, width, height, color, obj_type, world="kevin", image=None):
        self.rect = pygame.Rect(x, y, width, height)
        self.color = color
        self.type = obj_type
        self.world = world
        self.collected = False
        self.image = image
        self.bob_timer = 0

    def update(self):
        self.bob_timer += 0.1

    def draw(self, screen, camera_offset):
        if not self.collected:
            import math
            bob = math.sin(self.bob_timer) * 3
            if self.image:
                screen.blit(self.image, (self.rect.x + camera_offset, self.rect.y + bob))
            else:
                pygame.draw.rect(screen, self.color,
                               (self.rect.x + camera_offset, self.rect.y + bob, self.rect.width, self.rect.height))

# Enhanced Platform class for dimensional echoes
class Platform:
    def __init__(self, x, y, width, height, platform_type="normal", world="both"):
        self.rect = pygame.Rect(x, y, width, height)
        self.x = x
        self.y = y
        self.width = width
        self.height = height
        self.platform_type = platform_type  # normal, echo, puzzle, moving
        self.world = world  # kevin, zeth, or both
        self.active = True
        self.echo_timer = 0
        self.move_timer = 0
        self.original_x = x
        self.color = colors["GRAY"] if platform_type == "normal" else colors["LIGHT_BLUE"] if platform_type == "echo" else colors["ORANGE"]

    def update(self):
        if self.platform_type == "echo":
            self.echo_timer += 0.1
            # Echo platforms phase in and out
            import math
            self.active = math.sin(self.echo_timer) > 0
        elif self.platform_type == "moving":
            self.move_timer += 0.05
            import math
            self.x = self.original_x + math.sin(self.move_timer) * 100
            self.rect.x = self.x

    def draw(self, screen, camera_offset):
        if self.active:
            pygame.draw.rect(screen, self.color,
                           (self.x + camera_offset, self.y, self.width, self.height))
            if self.platform_type == "echo":
                # Add glowing effect for echo platforms
                pygame.draw.rect(screen, colors["WHITE"],
                               (self.x + camera_offset, self.y, self.width, self.height), 2)

# Initialize game objects for the dimensional rift guardians gameplay
def initialize_level(level):
    global crystals, shadow_entities, platforms, dimensional_stability, objects

    crystals = []
    shadow_entities = []
    platforms = []
    objects = []
    dimensional_stability = 100.0

    # Level 1: Tutorial - The First Rift
    if level == 1:
        # Kevin's world crystals (upper dimension)
        crystals.extend([
            Crystal(300, 350, "normal", "kevin"),
            Crystal(500, 200, "power", "kevin"),
            Crystal(700, 400, "normal", "kevin"),
            Crystal(900, 300, "echo", "kevin")
        ])

        # Zeth's world crystals (lower dimension)
        crystals.extend([
            Crystal(400, 300, "normal", "zeth"),
            Crystal(600, 150, "normal", "zeth"),
            Crystal(800, 350, "power", "zeth"),
            Crystal(1000, 250, "echo", "zeth")
        ])

        # Platforms for both worlds
        platforms.extend([
            Platform(200, 450, 120, 20, "normal", "both"),
            Platform(400, 350, 120, 20, "echo", "kevin"),
            Platform(600, 250, 120, 20, "normal", "both"),
            Platform(800, 450, 120, 20, "echo", "zeth"),
            Platform(1000, 200, 120, 20, "moving", "both")
        ])

        # Shadow entities
        shadow_entities.extend([
            ShadowEntity(350, 400, "basic", "kevin"),
            ShadowEntity(650, 200, "basic", "zeth"),
            ShadowEntity(850, 400, "advanced", "kevin")
        ])

        # Special objects
        objects.extend([
            GameObject(250, 400, 25, 25, colors["RED"], "energy_core", "kevin"),
            GameObject(750, 300, 25, 25, colors["BLUE"], "dimensional_key", "zeth"),
            GameObject(550, 180, 25, 25, colors["GREEN"], "health_crystal", "both")
        ])

    # Level 2: The Spreading Corruption
    elif level == 2:
        crystals.extend([
            Crystal(200, 400, "normal", "kevin"),
            Crystal(400, 250, "power", "kevin"),
            Crystal(600, 350, "echo", "kevin"),
            Crystal(800, 200, "normal", "kevin"),
            Crystal(1000, 400, "power", "kevin"),
            Crystal(300, 350, "normal", "zeth"),
            Crystal(500, 200, "echo", "zeth"),
            Crystal(700, 300, "normal", "zeth"),
            Crystal(900, 150, "power", "zeth"),
            Crystal(1100, 350, "echo", "zeth")
        ])

        platforms.extend([
            Platform(150, 450, 100, 20, "normal", "both"),
            Platform(300, 300, 100, 20, "echo", "kevin"),
            Platform(500, 400, 100, 20, "moving", "both"),
            Platform(700, 250, 100, 20, "echo", "zeth"),
            Platform(900, 350, 100, 20, "normal", "both"),
            Platform(1100, 200, 100, 20, "moving", "kevin")
        ])

        shadow_entities.extend([
            ShadowEntity(250, 400, "basic", "kevin"),
            ShadowEntity(450, 350, "advanced", "kevin"),
            ShadowEntity(650, 200, "basic", "zeth"),
            ShadowEntity(850, 300, "advanced", "zeth"),
            ShadowEntity(1050, 350, "basic", "kevin")
        ])

# Initialize first level
crystals = []
shadow_entities = []
platforms = []
objects = []





        

# What I will have on the menu
menu_options = [
                    "NEW",
                    "LOAD",
                    "SETTINGS",
                    "LANGUAGE",
                    "CREDITS",
                    "CONTROLS",
                    "HELP",
                    "EXIT"
                ]
selected_option = 0
options_rects = []


# save function
load_selected = 0
saved_files = []
saved_rects = []

def save_game(save_name):
     if not os.path.exists("saves"):
         os.makedirs("saves")
     file_path = f"saves/{save_name}.json"
     game_data = {
        "kevin_x": kevin.x,
        "kevin_y": kevin.y,
        "zeth_x": zeth.x,
        "zeth_y": zeth.y,
        "current_world": current_world
    }

     with open(file_path, "w") as f:
             json.dump(game_data,f)
     print(f"Saved game to {file_path}") #to make sure it works

def check_overwrite(save_name):
    global overwrite_confirm, overwrite_save_name
    file_path = f"saves/{save_name}.json"

    if os.path.exists(file_path):
        overwrite_confirm = True
        overwrite_save_name = save_name

        return False
    return True

def delete_save():
    global load_selected, saved_files, delete_confirm, delete_save_name

    if saved_files and not delete_confirm:
        delete_confirm = True
        delete_save_name = saved_files[load_selected]
        
    elif saved_files and delete_confirm:
        os.remove(delete_save_name)
        saved_files.pop(load_selected)
        load_selected = max(0, load_selected - 1)
        delete_confirm = False


#type file names
class TextInput:
    def __init__(self,x,y,width,height):
        self.rect = pygame.Rect(x,y,width,height)
        self.text = ""
        self.active = False

    def handle_event(self,event):
        if event.type == pygame.MOUSEBUTTONDOWN:
            self.active = self.rect.collidepoint(event.pos) 

        if self.active and event.type == pygame.KEYDOWN:
            if event.key == pygame.K_RETURN and self.text:
                return self.text   # returning whatever was typed

            elif event.key == pygame.K_BACKSPACE:
                self.text = self.text[:-1] # sapce if needed

            else:
                self.text += event.unicode # letters typed

        return None
    
    def draw(self,screen): # drawing the rects
        pygame.draw.rect(screen,colors["WHITE"],self.rect,2)
        text_surface = font.render(self.text,True,colors["WHITE"])

        screen.blit(text_surface, (self.rect.x + 5, self.rect.y + 5))

#scrolling
scroll_offset = 0
max_display = 8

# languages
languages =  ["English", "Japanese", "Arabic", "Ukrainian"]
language_selected = 0
current_language = "English"

# Game mechanics functions
def update_game():
    global dimensional_stability, game_timer, game_state

    # Update game timer
    game_timer += 1

    # Decrease dimensional stability over time (creates urgency)
    dimensional_stability -= 0.03

    # Update all game objects
    for crystal in crystals:
        crystal.update()

    for entity in shadow_entities:
        entity.update(kevin, zeth, current_world)

    for platform in platforms:
        platform.update()

    for obj in objects:
        obj.update()

    # Update player invulnerability
    if kevin.invulnerable_timer > 0:
        kevin.invulnerable_timer -= 1
    if zeth.invulnerable_timer > 0:
        zeth.invulnerable_timer -= 1

    # Check win/lose conditions
    if dimensional_stability <= 0 or (kevin.health <= 0 and zeth.health <= 0):
        game_state = GAME_OVER
    elif crystals_collected["kevin"] + crystals_collected["zeth"] >= total_crystals_needed:
        if game_level < max_levels:
            next_level()
        else:
            game_state = VICTORY

def next_level():
    global game_level
    game_level += 1
    initialize_level(game_level)
    kevin.x, kevin.y = 100, 400
    zeth.x, zeth.y = 100, 400
    kevin.health = min(kevin.max_health, kevin.health + 50)  # Heal on level up
    zeth.health = min(zeth.max_health, zeth.health + 50)

def check_collisions():
    global crystals_collected, shadow_entities_defeated

    # Current player
    player = kevin if current_world == "kevin" else zeth
    player_rect = pygame.Rect(player.x, player.y, player.width, player.height)

    # Crystal collection
    for crystal in crystals:
        if not crystal.collected and crystal.world == current_world:
            crystal_rect = pygame.Rect(crystal.x, crystal.y, crystal.width, crystal.height)
            if player_rect.colliderect(crystal_rect):
                crystal.collected = True
                crystals_collected[current_world] += 1
                if crystal.crystal_type == "power":
                    player.has_crystal_power = True
                    player.energy = player.max_energy
                elif crystal.crystal_type == "echo":
                    # Echo crystals affect both worlds
                    dimensional_stability += 10

    # Object collection
    for obj in objects:
        if not obj.collected and (obj.world == current_world or obj.world == "both"):
            if player_rect.colliderect(obj.rect):
                obj.collected = True
                inventory[current_world].append(obj)
                # Apply object effects
                if obj.type == "health_crystal":
                    player.health = min(player.max_health, player.health + 30)
                elif obj.type == "energy_core":
                    player.energy = player.max_energy

    # Shadow entity combat
    for entity in shadow_entities:
        if entity.is_alive and entity.world == current_world:
            entity_rect = pygame.Rect(entity.x, entity.y, entity.width, entity.height)
            if player_rect.colliderect(entity_rect):
                if player.has_crystal_power and player.energy > 20:
                    entity.health -= 30
                    player.energy -= 20
                    if entity.health <= 0:
                        entity.is_alive = False
                        shadow_entities_defeated += 1
                        dimensional_stability += 5  # Defeating enemies helps stability
                elif player.invulnerable_timer <= 0:
                    player.health -= 15
                    player.invulnerable_timer = 60  # 1 second of invulnerability

    # Platform collisions
    for platform in platforms:
        if platform.active and (platform.world == current_world or platform.world == "both"):
            platform_rect = pygame.Rect(platform.x, platform.y, platform.width, platform.height)
            if player_rect.colliderect(platform_rect) and player.y_velocity <= 0:
                player.y = platform.y - player.height
                player.y_velocity = 0
                player.on_ground = True

#inventory method
inventory = {"kevin": [], "zeth": []}
def show_inventory():
    screen.fill(colors["BLACK"])
    title = font.render("DIMENSIONAL INVENTORY", True, colors["WHITE"])
    screen.blit(title, (SCREEN_WIDTH//2 - 100, 50))

    # Show crystals collected
    crystal_text = font.render(f"Crystals: Kevin {crystals_collected['kevin']} | Zeth {crystals_collected['zeth']}", True, colors["CYAN"])
    screen.blit(crystal_text, (50, 100))

    # Show dimensional stability
    stability_color = colors["GREEN"] if dimensional_stability > 50 else colors["YELLOW"] if dimensional_stability > 25 else colors["RED"]
    stability_text = font.render(f"Dimensional Stability: {dimensional_stability:.1f}%", True, stability_color)
    screen.blit(stability_text, (50, 130))

    y_offset = 170
    for item in inventory[current_world]:
        item_text = font.render(f"• {item.type.replace('_', ' ').title()}", True, colors["WHITE"])
        screen.blit(item_text, (100, y_offset))
        y_offset += 30

    back_text = font.render("Press I to close", True, colors["WHITE"])
    screen.blit(back_text, (SCREEN_WIDTH//2 - 50, SCREEN_HEIGHT - 50))





RUNNING = True
while RUNNING:
    # Handle events


    pressed_keys = pygame.key.get_pressed()
    mouse_pos = pygame.mouse.get_pos()

    for event in pygame.event.get():
        if event.type == pygame.QUIT:
            RUNNING = False # quit

        if event.type == pygame.KEYDOWN:
            if event.key == pygame.K_ESCAPE and game_state == GAME:
                game_state = PAUSE  # Pause

            elif event.key == pygame.K_r and game_state == PAUSE:
                game_state = GAME

            elif event.key == pygame.K_q and game_state == PAUSE:
                RUNNING = False

            elif event.key == pygame.K_x and game_state == GAME:
                current_world = "kevin"

            elif event.key == pygame.K_z and game_state == GAME:
                current_world = "zeth"

            elif event.key == pygame.K_SPACE and game_state == GAME:
                # Crystal power attack
                player = kevin if current_world == "kevin" else zeth
                if player.has_crystal_power and player.energy > 30:
                    player.energy -= 30
                    # Damage nearby enemies with area attack
                    for entity in shadow_entities:
                        if entity.is_alive and entity.world == current_world:
                            distance = abs(entity.x - player.x) + abs(entity.y - player.y)
                            if distance < 120:
                                entity.health -= 60
                                if entity.health <= 0:
                                    entity.is_alive = False
                                    shadow_entities_defeated += 1

            elif event.key == pygame.K_m and game_state == PAUSE:
                game_state = MENU

            elif event.key == pygame.K_p and game_state == NOT_YET:
                game_state = MENU

            elif event.key == pygame.K_UP and game_state == MENU:
                selected_option = (selected_option - 1) % len(menu_options)

            elif event.key == pygame.K_DOWN and game_state == MENU:
                selected_option = (selected_option + 1) % len(menu_options)

            elif event.key == pygame.K_UP and game_state == LOAD:
                if len(saved_files) > 0:
                    load_selected = (load_selected - 1)% len(saved_files)

                    if load_selected < scroll_offset:
                        scroll_offset = load_selected

                    elif load_selected >= scroll_offset + max_display:
                        scroll_offset = max(0, load_selected - max_display +1)

            elif event.key == pygame.K_DOWN and game_state == LOAD:
                if len(saved_files) > 0:
                    load_selected = (load_selected + 1)% len(saved_files)

                    if load_selected >= scroll_offset+max_display:
                        scroll_offset = load_selected - max_display + 1
                        
                    elif load_selected < scroll_offset:
                        scroll_offset = load_selected


                if load_selected >= scroll_offset + max_display:
                    scroll_offset = min(len(saved_files) - max_display, scroll_offset + 1)

            elif event.key == pygame.K_UP and game_state == LANGUAGE:
                language_selected = max(0, language_selected - 1)
                if language_selected < scroll_offset:
                    scroll_offset = language_selected

            elif event.key == pygame.K_DOWN and game_state == LANGUAGE:
                language_selected = min(len(languages) - 1, language_selected + 1)
                if language_selected >= scroll_offset + max_display:
                    scroll_offset = min(len(languages) - max_display, language_selected)

            elif event.key == pygame.K_RETURN and game_state == LANGUAGE:
                current_language = languages[language_selected]
                lang_map = {"English": "en", "Japanese": "ja", "Arabic": "ar", "Ukrainian": "uk"}
                translation = gettext.translation('messages', localedir=local_direc, languages=[lang_map[current_language]], fallback=True)
                _ = translation.gettext
                game_state = MENU
                    
                    


            elif event.key == pygame.K_UP and  game_state == SETTINGS:
                settings_selected = (settings_selected - 1) % 4

            elif event.key == pygame.K_DOWN and game_state == SETTINGS:
                settings_selected = (settings_selected + 1) % 4

            elif event.key == pygame.K_LEFT and game_state == SETTINGS and settings_selected == 1:
                volume = max(0.0, volume - 0.1)
                pygame.mixer.music.set_volume(volume)

            elif event.key == pygame.K_RIGHT and game_state == SETTINGS and settings_selected == 1:
                volume = min(1.0, volume + 0.1)
                pygame.mixer.music.set_volume(volume)

            elif  game_state == SETTINGS and (event.key == pygame.K_RETURN or event.key == pygame.K_LEFT or event.key == pygame.K_RIGHT) and settings_selected == 0:
                music_on = not music_on
                if music_on:
                    pygame.mixer.music.play(-1)
                else:
                    pygame.mixer.music.stop()
                    
            elif game_state == SETTINGS and settings_selected == 2:
                if event.key == pygame.K_RIGHT:
                    brightness = min(brightness_max, brightness + brightness_step)                
                elif event.key == pygame.K_LEFT:
                  brightness = max(brightness_min, brightness - brightness_step)

            elif game_state == STORY and event.key == pygame.K_RETURN:
                game_state = GAME

            elif event.key == pygame.K_i and game_state == GAME:
                game_state = INVENTORY

            elif event.key == pygame.K_i and game_state == INVENTORY:
                game_state = GAME

            elif event.key == pygame.K_RETURN and (game_state == GAME_OVER or game_state == VICTORY):
                # Restart game
                game_level = 1
                crystals_collected["kevin"] = 0
                crystals_collected["zeth"] = 0
                dimensional_stability = 100.0
                game_timer = 0
                shadow_entities_defeated = 0
                puzzles_solved = 0

                # Reset players
                kevin.x, kevin.y = 100, 400
                kevin.y_velocity = 0
                kevin.health = kevin.max_health
                kevin.energy = kevin.max_energy
                kevin.has_crystal_power = False
                kevin.invulnerable_timer = 0

                zeth.x, zeth.y = 100, 400
                zeth.y_velocity = 0
                zeth.health = zeth.max_health
                zeth.energy = zeth.max_energy
                zeth.has_crystal_power = False
                zeth.invulnerable_timer = 0

                game_state = MENU

            
            elif event.key == pygame.K_d and game_state == LOAD:
                delete_save()

            elif event.key == pygame.K_y and game_state == LOAD and delete_confirm:
                delete_save()

            elif event.key == pygame.K_n and game_state == LOAD and delete_confirm:
                delete_confirm = False

            elif event.key == pygame.K_y and game_state == PAUSE and overwrite_confirm:
                save_game(overwrite_save_name)
                overwrite_confirm = False
                save_input_active = False
                text_input = None

            elif event.key == pygame.K_n and game_state == PAUSE and overwrite_confirm:
                overwrite_confirm = False
                save_input_active = True
                text_input = TextInput(SCREEN_WIDTH // 2 - 100, SCREEN_HEIGHT // 2 + 50, 200, 30)

            elif event.key == pygame.K_RETURN and game_state == LOAD:

                if saved_files:
                    selected_save = saved_files[load_selected]
                    try:
                        with open(selected_save, "r") as f:
                            game_data = json.load(f)
                            kevin.x = game_data["kevin_x"]
                            kevin.y = game_data["kevin_y"]
                            zeth.x = game_data["zeth_x"]
                            zeth.y = game_data["zeth_y"]
                            current_world = game_data["current_world"]
                            game_state = GAME
                    except Exception as e:
                        game_state = NOT_YET
                        
            elif game_state in [LOAD, SETTINGS, LANGUAGE, HELP, CREDITS, CONTROLS ]:
                if event.key == pygame.K_b:
                    game_state = MENU

            elif event.key == pygame.K_RETURN:
                selected = menu_options[selected_option]

                if selected == "NEW":
                    # Reset game state for new game
                    game_level = 1
                    crystals_collected["kevin"] = 0
                    crystals_collected["zeth"] = 0
                    dimensional_stability = 100.0
                    game_timer = 0
                    shadow_entities_defeated = 0
                    puzzles_solved = 0

                    # Reset players
                    kevin.x, kevin.y = 100, 400
                    kevin.y_velocity = 0
                    kevin.health = kevin.max_health
                    kevin.energy = kevin.max_energy
                    kevin.has_crystal_power = False
                    kevin.invulnerable_timer = 0

                    zeth.x, zeth.y = 100, 400
                    zeth.y_velocity = 0
                    zeth.health = zeth.max_health
                    zeth.energy = zeth.max_energy
                    zeth.has_crystal_power = False
                    zeth.invulnerable_timer = 0

                    # Initialize first level
                    initialize_level(1)
                    game_state = STORY

                elif selected == "EXIT":
                    RUNNING = False

                elif selected == "LOAD":
                    game_state = LOAD

                elif selected == "CREDITS":
                    game_state = CREDITS

                else:
                    game_state = NOT_YET

            elif event.key == pygame.K_s and game_state == PAUSE:
                save_input_active = True
                text_input = TextInput(SCREEN_WIDTH // 2 - 100, SCREEN_HEIGHT // 2 + 50, 200, 30)


        if save_input_active and text_input:
            result = text_input.handle_event(event)

            if result:
                save_name = result

                if check_overwrite(save_name):
                    save_game(save_name)
                    save_input_active = False
                    text_input = None
        
        if event.type == pygame.MOUSEWHEEL:
            if game_state == LOAD:
                if event.y > 0:  # Scroll up
                    if len(saved_files) > 0:
                        if event.y > 0:
                            load_selected = (load_selected-1)%len(saved_files)

                            if load_selected < scroll_offset:
                                scroll_offset = load_selected

                            elif load_selected >= scroll_offset + max_display:
                                scroll_offset = max(0, load_selected - max_display +1)


                elif event.y < 0:  # Scroll down
                    load_selected = (load_selected + 1) % len(saved_files)

                    if load_selected >= scroll_offset + max_display:
                        scroll_offset = load_selected - max_display + 1

                    elif load_selected < scroll_offset:
                        scroll_offset = load_selected

            elif game_state == LANGUAGE:

                if event.y > 0:  # Scroll up
                        if event.y > 0:
                            language_selected = (language_selected - 1) % len(languages)

                        if language_selected < scroll_offset:
                            scroll_offset = language_selected

                        elif language_selected >= scroll_offset + max_display:
                            scroll_offset = max(0, language_selected - max_display + 1)



                elif event.y < 0:  # Scroll down
                    language_selected = (language_selected + 1) % len(languages)

                    if language_selected >= scroll_offset + max_display:
                        scroll_offset = language_selected - max_display + 1

                    elif language_selected < scroll_offset:
                        scroll_offset = language_selected



        if event.type == pygame.MOUSEBUTTONDOWN and event.button == 1:  # Left-click
                if game_state == MENU:
                    for i, rect in enumerate(options_rects):
                        if rect.collidepoint(event.pos):
                            selected_option = i
                            selected = menu_options[selected_option]
                            if selected == "NEW":
                                # Reset game state for new game
                                game_level = 1
                                crystals_collected["kevin"] = 0
                                crystals_collected["zeth"] = 0
                                dimensional_stability = 100.0
                                game_timer = 0
                                shadow_entities_defeated = 0
                                puzzles_solved = 0

                                # Reset players
                                kevin.x, kevin.y = 100, 400
                                kevin.y_velocity = 0
                                kevin.health = kevin.max_health
                                kevin.energy = kevin.max_energy
                                kevin.has_crystal_power = False
                                kevin.invulnerable_timer = 0

                                zeth.x, zeth.y = 100, 400
                                zeth.y_velocity = 0
                                zeth.health = zeth.max_health
                                zeth.energy = zeth.max_energy
                                zeth.has_crystal_power = False
                                zeth.invulnerable_timer = 0

                                # Initialize first level
                                initialize_level(1)
                                game_state = STORY
                            elif selected == "EXIT":
                                RUNNING = False
                            elif selected == "LOAD":
                                game_state = LOAD
                            elif selected == "CREDITS":
                                game_state = CREDITS
                            elif selected == "CONTROLS":
                                game_state = CONTROLS
                            elif selected == "HELP":
                                game_state = HELP
                            elif selected == "SETTINGS":
                                game_state = SETTINGS
                            elif selected == "LANGUAGE":
                                game_state = LANGUAGE
                            else:
                                game_state = NOT_YET
                           
                elif game_state in [LOAD, SETTINGS, LANGUAGE, HELP, CREDITS, CONTROLS]:
                    if back_rect and back_rect.collidepoint(event.pos):
                        game_state = MENU

                elif game_state == STORY:
                    if continue_rect and continue_rect.collidepoint(event.pos):
                        game_state = GAME

                                            
                    elif game_state == LANGUAGE:
                        for i, rect in enumerate(options_rects):
                            if rect.collidepoint(event.pos):
                                language_selected = i + scroll_offset
                                current_language = languages[language_selected]
                                game_state = MENU

                    elif game_state == SETTINGS:
                        for i, rect in enumerate(options_rects):
                            if rect.collidepoint(event.pos):
                                settings_selected = i
                                if i == 0:  # music toggle
                                    music_on = not music_on
                                    if music_on:
                                        pygame.mixer.music.play(-1)
                                    else:
                                        pygame.mixer.music.stop()
                                elif i == 3:  # back button
                                    game_state = MENU
                                    
                        volume_bar_rect = pygame.Rect(SCREEN_WIDTH // 2 + 50, SCREEN_HEIGHT // 2 + 25, 100, 20)
                        if volume_bar_rect.collidepoint(event.pos):
                            click_x = event.pos[0] - (SCREEN_WIDTH // 2 + 50)
                            volume = max(0.0, min(1.0, click_x / 100.0))
                            pygame.mixer.music.set_volume(volume)
                            settings_selected = 1
                            
                        brightness_bar_rect = pygame.Rect(SCREEN_WIDTH // 2 + 50, SCREEN_HEIGHT // 2 + 65, 100, 20)
                        if brightness_bar_rect.collidepoint(event.pos):
                            click_x = event.pos[0] - (SCREEN_WIDTH // 2 + 50)
                            brightness = max(0.0, min(1.0, click_x / 100.0))
                            settings_selected = 2
                                
                    else:
                        for i, rect in enumerate(saved_rects):
                            if rect.collidepoint(event.pos):
                                load_selected = i + scroll_offset
                                selected_save = saved_files[load_selected]
                                try:
                                    with open(selected_save, "r") as f:
                                        game_data = json.load(f)
                                        kevin.x = game_data["kevin_x"]
                                        kevin.y = game_data["kevin_y"]
                                        zeth.x = game_data["zeth_x"]
                                        zeth.y = game_data["zeth_y"]
                                        current_world = game_data["current_world"]
                                        game_state = GAME
                                except Exception as e:
                                    game_state = NOT_YET
                
              


        

# Handling game states
    if game_state == MENU: # menu logic
       



        screen.fill(colors["PINK"])
        title_text = font.render(_("GAME"), True, colors["WHITE"])        
        title_rect = title_text.get_rect(center=(SCREEN_WIDTH // 2, SCREEN_HEIGHT // 8))
        screen.blit(title_text, title_rect)
        options_rects.clear()

        for i, option in enumerate(menu_options):
            text = font.render(_(option), True, colors["BLACK"])  # Temporary color
            rect = text.get_rect(center=(SCREEN_WIDTH // 2, SCREEN_HEIGHT // 4 + i * 40))
            options_rects.append(rect)

        for i, option in enumerate (menu_options):
            hover = options_rects[i].collidepoint(mouse_pos) if i < len(options_rects) else False
            color = colors["RED"] if i == selected_option else colors["BLUE"] if hover else colors["BLACK"]

            text = font.render(_(option), True, color)
            rect = text.get_rect(center=(SCREEN_WIDTH // 2, SCREEN_HEIGHT //4 +i*40))
            screen.blit(text, rect)
            
    elif game_state ==  NOT_YET:# not yet(not implemented) logic
        screen.fill(colors["YELLOW"])
        title_text = font.render(_("NOT YET"), True, colors["WHITE"])        
        title_rect = title_text.get_rect(center=(SCREEN_WIDTH // 2, SCREEN_HEIGHT // 2))
        screen.blit(title_text, title_rect)

        

    elif game_state == LOAD: # load logic
        screen.fill(colors["PINK"])
        title_text = font.render(_("LOAD GAME"), True, colors["WHITE"])        
        title_rect = title_text.get_rect(center=(SCREEN_WIDTH // 2, SCREEN_HEIGHT // 8))

        screen.blit(title_text, title_rect)

        saved_files = glob.glob("saves/*.json")
        saved_rects.clear()

        if not saved_files:
            no_saves_text = font.render(_("No saved games found!"), True, colors["RED"])
            no_saves_rect = no_saves_text.get_rect(center=(SCREEN_WIDTH // 2, SCREEN_HEIGHT // 2))
            screen.blit(no_saves_text, no_saves_rect)

        else:
            saved_files.sort(key=os.path.getmtime, reverse=True)

            for i, saved_file in enumerate(saved_files[scroll_offset:scroll_offset + max_display]):

                file_name = os.path.basename(saved_file)
                mod_time = time.strftime("%Y-%m-%d %H:%M", time.localtime(os.path.getmtime(saved_file)))
                display_text = f"{file_name} ({mod_time})"

                rect = pygame.Rect(SCREEN_WIDTH // 2 - 150, SCREEN_HEIGHT // 4 + i * 40 - 15, 300, 30)
                hover = rect.collidepoint(mouse_pos)
                color = colors["RED"] if i + scroll_offset == load_selected else colors["BLUE"] if hover else colors["BLACK"]
                text = font.render(display_text, True, color)

                saved_rects.append(rect)
                screen.blit(text, text.get_rect(center=(SCREEN_WIDTH // 2, SCREEN_HEIGHT // 4 + i * 40)))

        if delete_confirm:
            confirm_text = font.render(f"{_('Delete')} {os.path.basename(delete_save_name)}? (Y/N)", True, colors["RED"])
            confirm_rect = confirm_text.get_rect(center=(SCREEN_WIDTH // 2, SCREEN_HEIGHT // 2 + 100))
            screen.blit(confirm_text, confirm_rect)

        back_text = font.render(_("Back (B)"), True, colors["BLUE"] if back_rect and back_rect.collidepoint(mouse_pos) else colors["WHITE"])
        back_rect = back_text.get_rect(center=(SCREEN_WIDTH // 2, SCREEN_HEIGHT - 50))
        screen.blit(back_text, back_rect)

    elif game_state == CREDITS: # credits logic
        screen.fill(colors["PINK"])

        title_text = font.render(_("CREDITS"), True, colors["WHITE"])        
        title_rect = title_text.get_rect(center=(SCREEN_WIDTH // 2, SCREEN_HEIGHT // 4))
        
        name_text = font.render(_("Created by Malak Awadallah"), True, colors["WHITE"])
        name_rect = name_text.get_rect(center=(SCREEN_WIDTH // 2, SCREEN_HEIGHT // 2))

        back_text = font.render(_("Back (B)"), True, colors["BLUE"] if back_rect and back_rect.collidepoint(mouse_pos) else colors["WHITE"])
        back_rect = back_text.get_rect(center=(SCREEN_WIDTH // 2, SCREEN_HEIGHT - 50))

        screen.blit(title_text, title_rect)
        screen.blit(name_text, name_rect)
        screen.blit(back_text, back_rect)

    elif game_state == CONTROLS: # controls logic
        screen.fill(colors["PINK"])

        title_text = font.render(_("CONTROLS"), True, colors["WHITE"])
        title_rect = title_text.get_rect(center=(SCREEN_WIDTH // 2, SCREEN_HEIGHT // 4))

        kevin_text = font.render(_("Kevin: Left Arrow (Left), Right Arrow (Right), Down Arrow (Jump)"), True, colors["WHITE"])
        kevin_rect = kevin_text.get_rect(center=(SCREEN_WIDTH // 2, SCREEN_HEIGHT // 2 - 20))

        zeth_text = font.render(_("Zeth: A (Left), D (Right), W (Jump)"), True, colors["WHITE"])
        zeth_rect = zeth_text.get_rect(center=(SCREEN_WIDTH // 2, SCREEN_HEIGHT // 2 + 20))

        back_text = font.render(_("Back (B)"), True, colors["BLUE"] if back_rect and back_rect.collidepoint(mouse_pos) else colors["WHITE"])
        back_rect = back_text.get_rect(center=(SCREEN_WIDTH // 2, SCREEN_HEIGHT - 50))

        screen.blit(title_text, title_rect)
        screen.blit(kevin_text, kevin_rect)
        screen.blit(zeth_text, zeth_rect)
        screen.blit(back_text, back_rect)

    elif game_state == HELP: # help logic
        screen.fill(colors["PINK"])

        title_text = font.render(_("HELP"), True, colors["WHITE"])
        title_rect = title_text.get_rect(center=(SCREEN_WIDTH // 2, SCREEN_HEIGHT // 4))

        help_text1 = font.render(_("Press X (Kevin) or Y (Zeth) to switch worlds."), True, colors["WHITE"])
        help_text1_rect = help_text1.get_rect(center=(SCREEN_WIDTH // 2, SCREEN_HEIGHT // 2))

        back_text = font.render(_("Back (B)"), True, colors["BLUE"] if back_rect and back_rect.collidepoint(mouse_pos) else colors["WHITE"])
        back_rect = back_text.get_rect(center=(SCREEN_WIDTH // 2, SCREEN_HEIGHT - 50))

        screen.blit(title_text, title_rect)
        screen.blit(help_text1, help_text1_rect)
        screen.blit(back_text, back_rect)

    elif game_state == SETTINGS:  # settings logic
        screen.fill(colors["PINK"])

        title_text = font.render(_("SETTINGS"), True, colors["WHITE"])
        title_rect = title_text.get_rect(center=(SCREEN_WIDTH // 2, SCREEN_HEIGHT // 4))
        screen.blit(title_text, title_rect)

        options_rects.clear()

        # Music toggle
        music_text = _("Music: ON") if music_on else _("Music: OFF")
        music_surface = font.render(music_text, True, colors["BLACK"])
        music_rect = music_surface.get_rect(center=(SCREEN_WIDTH // 2, SCREEN_HEIGHT // 2 - 40))
        options_rects.append(music_rect)
        hover = music_rect.collidepoint(mouse_pos)
        color = colors["RED"] if settings_selected == 0 else colors["BLUE"] if hover else colors["BLACK"]
        music_surface = font.render(music_text, True, color)
        screen.blit(music_surface, music_rect)

        # Volume label
        volume_label = font.render(_("Volume"), True, colors["BLACK"])
        volume_label_rect = volume_label.get_rect(center=(SCREEN_WIDTH // 2 - 50, SCREEN_HEIGHT // 2 + 40))
        options_rects.append(volume_label_rect)
        hover = volume_label_rect.collidepoint(mouse_pos)
        color = colors["RED"] if settings_selected == 1 else colors["BLUE"] if hover else colors["BLACK"]
        volume_surface = font.render(_("Volume"), True, color)
        screen.blit(volume_surface, volume_label_rect)

        # Volume bar 
        volume_bar_rect = pygame.Rect(SCREEN_WIDTH // 2 + 50, SCREEN_HEIGHT // 2 + 25, 100, 20)
        hover = volume_bar_rect.collidepoint(mouse_pos)
        bar_color = colors["BLUE"] if not hover else colors["GREEN"]  # Green when hovered
        pygame.draw.rect(screen, colors["BLACK"], volume_bar_rect, 2)
        filled_volume_rect = pygame.Rect(SCREEN_WIDTH // 2 + 50, SCREEN_HEIGHT // 2 + 25, 100 * volume, 20)
        pygame.draw.rect(screen, bar_color, filled_volume_rect)
        
        # Brightness label
        brightness_label = font.render(_("Brightness"), True, colors["BLACK"])
        brightness_label_rect = brightness_label.get_rect(center=(SCREEN_WIDTH // 2 - 50, SCREEN_HEIGHT // 2 + 80))
        options_rects.append(brightness_label_rect)
        hover = brightness_label_rect.collidepoint(mouse_pos)
        color = colors["RED"] if settings_selected == 2 else colors["BLUE"] if hover else colors["BLACK"]
        brightness_surface = font.render(_("Brightness"), True, color)
        screen.blit(brightness_surface, brightness_label_rect)
        
        # Brightness bar
        brightness_bar_rect = pygame.Rect(SCREEN_WIDTH // 2 + 50, SCREEN_HEIGHT // 2 + 65, 100, 20)
        hover = brightness_bar_rect.collidepoint(mouse_pos)
        bar_color = colors["BLUE"] if not hover else colors["GREEN"]
        pygame.draw.rect(screen, colors["BLACK"], brightness_bar_rect, 2)
        filled_brightness_rect = pygame.Rect(SCREEN_WIDTH // 2 + 50, SCREEN_HEIGHT // 2 + 65, 100 * brightness, 20)
        pygame.draw.rect(screen, bar_color, filled_brightness_rect)

        # Back button
        back_text = font.render(_("Back (B)"), True, colors["BLACK"])
        back_rect = back_text.get_rect(center=(SCREEN_WIDTH // 2, SCREEN_HEIGHT - 50))
        options_rects.append(back_rect)
        hover = back_rect.collidepoint(mouse_pos)
        color = colors["BLUE"] if hover else colors["BLACK"]
        back_surface = font.render(_("Back (B)"), True, color)
        screen.blit(back_surface, back_rect)
        
    

    elif game_state == LANGUAGE: # language logic
        screen.fill(colors["PINK"])

        title_text = font.render(_("LANGUAGES"), True, colors["WHITE"])
        title_rect = title_text.get_rect(center=(SCREEN_WIDTH // 2, SCREEN_HEIGHT //4))

        screen.blit(title_text, title_rect)

        options_rects.clear()

        for i, lang in enumerate(languages[scroll_offset:scroll_offset + max_display]):

            text = font.render(lang, True, colors["BLACK"])
            rect = text.get_rect(center=(SCREEN_WIDTH // 2, SCREEN_HEIGHT // 3 + i * 45))
            options_rects.append(rect)

        for i, lang in enumerate(languages[scroll_offset:scroll_offset + max_display]):

            hover = options_rects[i].collidepoint(mouse_pos)
            color = colors["RED"] if i + scroll_offset == language_selected else colors["BLUE"] if hover else colors["BLACK"]
            text = font.render(lang, True, color)
            screen.blit(text, options_rects[i])

        back_text = font.render(_("Back (B)"), True, colors["BLUE"] if back_rect and back_rect.collidepoint(mouse_pos) else colors["WHITE"])
        back_rect = back_text.get_rect(center=(SCREEN_WIDTH // 2, SCREEN_HEIGHT - 50))

        screen.blit(back_text, back_rect)

    elif game_state == STORY:
        pygame.mixer.music.stop()

        screen.fill(colors["BLACK"])

        # Title
        title_text = large_font.render(_("THE DIMENSIONAL RIFT GUARDIANS"), True, colors["GOLD"])
        title_rect = title_text.get_rect(center=(SCREEN_WIDTH // 2, 80))
        screen.blit(title_text, title_rect)

        # Story text
        story_lines = [
            _("Two parallel dimensions are colliding..."),
            _("Kevin guards the Upper Realm of Light"),
            _("Zeth protects the Lower Realm of Shadows"),
            _(""),
            _("Dimensional rifts are spreading chaos!"),
            _("Collect Crystal Fragments to seal the rifts"),
            _("Defeat Shadow Entities corrupting both worlds"),
            _("Work together across dimensions to save reality!"),
            _(""),
            _("Controls:"),
            _("Kevin: Arrow Keys (Left/Right/Down to jump)"),
            _("Zeth: A/D/W keys"),
            _("Switch worlds: X (Kevin) / Z (Zeth)"),
            _("Crystal Power Attack: SPACE"),
            _("Inventory: I")
        ]

        y_offset = 150
        for line in story_lines:
            if line:  # Skip empty lines
                text_color = colors["CYAN"] if line.startswith(_("Controls:")) else colors["WHITE"]
                story_text = small_font.render(line, True, text_color)
                story_rect = story_text.get_rect(center=(SCREEN_WIDTH // 2, y_offset))
                screen.blit(story_text, story_rect)
            y_offset += 25

        mouse_pos = pygame.mouse.get_pos()
        continue_rect = pygame.Rect(0, 0, 250, 40)
        continue_rect.center = (SCREEN_WIDTH//2, SCREEN_HEIGHT-50)
        hover = continue_rect.collidepoint(mouse_pos)
        color = colors["GOLD"] if hover else colors["WHITE"]
        continue_text = font.render(_("BEGIN ADVENTURE --->"), True, color)
        text_rect = continue_text.get_rect(center=continue_rect.center)
        screen.blit(continue_text, text_rect)
        

    


    elif game_state == GAME: # THE DIMENSIONAL RIFT GUARDIANS - Main gameplay
        # Update game mechanics
        update_game()
        check_collisions()

        if pygame.mixer.music.get_busy():
           pygame.mixer.music.stop()
        elif game_state == MENU:
           if not pygame.mixer.music.get_busy():
               pygame.mixer.music.play(-1)

        #camera offset for scrolling
        if current_world == "kevin":
            camera_offset["kevin"] = -kevin.x + SCREEN_WIDTH // 4  # Keep player at 1/4 of screen
            current_camera = camera_offset["kevin"]
        else:
            camera_offset["zeth"] = -zeth.x + SCREEN_WIDTH // 4
            current_camera = camera_offset["zeth"]

        # Draw world backgrounds with dimensional effects
        if current_world == "kevin":
            # Kevin's world - Upper Realm of Light
            screen.fill(colors["PINK"])
            # Add some atmospheric effects
            for i in range(5):
                import random
                x = random.randint(0, SCREEN_WIDTH)
                y = random.randint(0, SCREEN_HEIGHT)
                pygame.draw.circle(screen, colors["LIGHT_BLUE"], (x, y), 2)
        elif current_world == "zeth":
            # Zeth's world - Lower Realm of Shadows
            screen.fill(colors["DARK_GREEN"])
            # Add some atmospheric effects
            for i in range(5):
                import random
                x = random.randint(0, SCREEN_WIDTH)
                y = random.randint(0, SCREEN_HEIGHT)
                pygame.draw.circle(screen, colors["PURPLE"], (x, y), 2)

        # Draw platforms
        for platform in platforms:
            if platform.world == current_world or platform.world == "both":
                platform.draw(screen, current_camera)

        # Draw crystals
        for crystal in crystals:
            if crystal.world == current_world:
                crystal.draw(screen, current_camera)

        # Draw objects
        for obj in objects:
            if obj.world == current_world or obj.world == "both":
                obj.draw(screen, current_camera)

        # Draw shadow entities
        for entity in shadow_entities:
            if entity.world == current_world:
                entity.draw(screen, current_camera)

        # Draw players with invulnerability effect
        if current_world == "kevin":
            if kevin.invulnerable_timer > 0 and kevin.invulnerable_timer % 10 < 5:
                # Flashing effect when invulnerable
                pass
            else:
                pygame.draw.rect(screen, kevin.color, (kevin.x + current_camera, kevin.y, kevin.width, kevin.height))
                # Crystal power glow
                if kevin.has_crystal_power:
                    pygame.draw.rect(screen, colors["CYAN"], (kevin.x + current_camera - 2, kevin.y - 2, kevin.width + 4, kevin.height + 4), 2)

        elif current_world == "zeth":
            if zeth.invulnerable_timer > 0 and zeth.invulnerable_timer % 10 < 5:
                # Flashing effect when invulnerable
                pass
            else:
                pygame.draw.rect(screen, zeth.color, (zeth.x + current_camera, zeth.y, zeth.width, zeth.height))
                # Crystal power glow
                if zeth.has_crystal_power:
                    pygame.draw.rect(screen, colors["CYAN"], (zeth.x + current_camera - 2, zeth.y - 2, zeth.width + 4, zeth.height + 4), 2)

        # Draw UI elements
        current_player = kevin if current_world == "kevin" else zeth

        # Health bar
        health_ratio = current_player.health / current_player.max_health
        health_color = colors["GREEN"] if health_ratio > 0.6 else colors["YELLOW"] if health_ratio > 0.3 else colors["RED"]
        pygame.draw.rect(screen, colors["BLACK"], (10, 10, 204, 24))
        pygame.draw.rect(screen, health_color, (12, 12, 200 * health_ratio, 20))
        health_text = small_font.render(f"Health: {current_player.health}/{current_player.max_health}", True, colors["WHITE"])
        screen.blit(health_text, (15, 15))

        # Energy bar
        energy_ratio = current_player.energy / current_player.max_energy
        pygame.draw.rect(screen, colors["BLACK"], (10, 40, 204, 24))
        pygame.draw.rect(screen, colors["CYAN"], (12, 42, 200 * energy_ratio, 20))
        energy_text = small_font.render(f"Energy: {current_player.energy}/{current_player.max_energy}", True, colors["WHITE"])
        screen.blit(energy_text, (15, 45))

        # Crystal count
        crystal_text = small_font.render(f"Crystals: K:{crystals_collected['kevin']} Z:{crystals_collected['zeth']}", True, colors["CYAN"])
        screen.blit(crystal_text, (10, 75))

        # Dimensional stability
        stability_color = colors["GREEN"] if dimensional_stability > 50 else colors["YELLOW"] if dimensional_stability > 25 else colors["RED"]
        stability_text = small_font.render(f"Dimensional Stability: {dimensional_stability:.1f}%", True, stability_color)
        screen.blit(stability_text, (10, 100))

        # Current world indicator
        world_text = font.render(f"Current World: {current_world.title()}", True, colors["GOLD"])
        screen.blit(world_text, (SCREEN_WIDTH - 250, 10))

        # Level indicator
        level_text = small_font.render(f"Level: {game_level}/{max_levels}", True, colors["WHITE"])
        screen.blit(level_text, (SCREEN_WIDTH - 150, 45))

        # Instructions
        if game_timer < 300:  # Show for first 5 seconds
            instruction_text = small_font.render("X/Z: Switch Worlds | SPACE: Crystal Attack | I: Inventory", True, colors["WHITE"])
            screen.blit(instruction_text, (SCREEN_WIDTH//2 - 200, SCREEN_HEIGHT - 30))

        if current_world == "kevin":

            if pressed_keys[kevin.controls[0]] and kevin.x > 0:
                kevin.x -= kevin.speed # Left
                print(kevin.x)

            if pressed_keys[kevin.controls[1]]:  
                kevin.x += kevin.speed # Right

            if pressed_keys[kevin.controls[2]] and kevin.on_ground:
                kevin.y_velocity = 10
                kevin.on_ground = False

        elif current_world == "zeth":

            if pressed_keys[zeth.controls[0]] and zeth.x > 0:
                zeth.x -= zeth.speed # Left

            if pressed_keys[zeth.controls[1]]:  
                zeth.x += zeth.speed  # Right

            if pressed_keys[zeth.controls[2]] and zeth.on_ground:
                zeth.y_velocity = -10
                zeth.on_ground = False

        kevin_rect = pygame.Rect(kevin.x, kevin.y, kevin.width, kevin.height)
        zeth_rect = pygame.Rect(zeth.x, zeth.y, zeth.width, zeth.height)

        kevin.on_ground = False
        zeth.on_ground = False
        
   
        kevin.y_velocity -= 0.5
        kevin.y += kevin.y_velocity
        kevin.on_ground = False

        if kevin.y < 0:
            kevin.y = 0
            kevin.y_velocity = 0
            kevin.on_ground = True

        zeth.y_velocity += 0.5
        zeth.y += zeth.y_velocity
        zeth.on_ground = False

        if zeth.y > SCREEN_HEIGHT - zeth.height:
            zeth.y = SCREEN_HEIGHT - zeth.height
            zeth.y_velocity = 0
            zeth.on_ground = True

    elif game_state == INVENTORY:
        show_inventory()

    elif game_state == GAME_OVER:
        screen.fill(colors["BLACK"])

        # Game Over title
        game_over_text = large_font.render(_("DIMENSIONAL COLLAPSE"), True, colors["RED"])
        game_over_rect = game_over_text.get_rect(center=(SCREEN_WIDTH // 2, 150))
        screen.blit(game_over_text, game_over_rect)

        # Failure message
        failure_text = font.render(_("The dimensions have merged into chaos..."), True, colors["WHITE"])
        failure_rect = failure_text.get_rect(center=(SCREEN_WIDTH // 2, 220))
        screen.blit(failure_text, failure_rect)

        # Stats
        stats_lines = [
            f"Crystals Collected: Kevin {crystals_collected['kevin']}, Zeth {crystals_collected['zeth']}",
            f"Shadow Entities Defeated: {shadow_entities_defeated}",
            f"Time Survived: {game_timer // 60} seconds",
            f"Final Stability: {dimensional_stability:.1f}%"
        ]

        y_offset = 280
        for line in stats_lines:
            stat_text = small_font.render(line, True, colors["WHITE"])
            stat_rect = stat_text.get_rect(center=(SCREEN_WIDTH // 2, y_offset))
            screen.blit(stat_text, stat_rect)
            y_offset += 30

        # Restart instruction
        restart_text = font.render(_("Press ENTER to try again"), True, colors["YELLOW"])
        restart_rect = restart_text.get_rect(center=(SCREEN_WIDTH // 2, SCREEN_HEIGHT - 100))
        screen.blit(restart_text, restart_rect)

    elif game_state == VICTORY:
        screen.fill(colors["BLACK"])

        # Victory title
        victory_text = large_font.render(_("DIMENSIONS SAVED!"), True, colors["GOLD"])
        victory_rect = victory_text.get_rect(center=(SCREEN_WIDTH // 2, 150))
        screen.blit(victory_text, victory_rect)

        # Success message
        success_text = font.render(_("Kevin and Zeth have sealed all the rifts!"), True, colors["WHITE"])
        success_rect = success_text.get_rect(center=(SCREEN_WIDTH // 2, 220))
        screen.blit(success_text, success_rect)

        # Final stats
        final_stats = [
            f"Total Crystals Collected: {crystals_collected['kevin'] + crystals_collected['zeth']}",
            f"Shadow Entities Defeated: {shadow_entities_defeated}",
            f"Completion Time: {game_timer // 60} seconds",
            f"Final Stability: {dimensional_stability:.1f}%",
            "",
            "You are true Dimensional Guardians!"
        ]

        y_offset = 280
        for line in final_stats:
            if line:
                stat_text = small_font.render(line, True, colors["CYAN"] if "Guardians" in line else colors["WHITE"])
                stat_rect = stat_text.get_rect(center=(SCREEN_WIDTH // 2, y_offset))
                screen.blit(stat_text, stat_rect)
            y_offset += 25

        # Restart instruction
        restart_text = font.render(_("Press ENTER to play again"), True, colors["YELLOW"])
        restart_rect = restart_text.get_rect(center=(SCREEN_WIDTH // 2, SCREEN_HEIGHT - 100))
        screen.blit(restart_text, restart_rect)


    elif game_state == PAUSE:  # pausing logic
        pressed_keys = pygame.key.get_pressed()
        for event in pygame.event.get():
                if event.type == pygame.QUIT:
                    break


        title_text = font.render(_("YOU PAUSED"), True, colors["WHITE"])
        title_rect = title_text.get_rect(center=(SCREEN_WIDTH // 2, SCREEN_HEIGHT // 4))
        screen.blit(title_text, title_rect)

        text = font.render(_("Paused: R to Resume, Q to Quit, S to Save, M to go back to Menu"), True, colors["WHITE"])
        text_rect = text.get_rect(center=(SCREEN_WIDTH // 2, SCREEN_HEIGHT // 2))
        screen.blit(text, text_rect)

        if save_input_active and text_input:
            text_input.draw(screen)
        if overwrite_confirm:
            confirm_text = font.render(f"{_('Overwrite')} {overwrite_save_name}.json? (Y/N)", True, colors["RED"])
            confirm_rect = confirm_text.get_rect(center=(SCREEN_WIDTH // 2, SCREEN_HEIGHT // 2 + 100))
            screen.blit(confirm_text, confirm_rect)
                    
                    
    # Overlay
    overlay = pygame.Surface((SCREEN_WIDTH, SCREEN_HEIGHT))
    overlay.fill((0, 0, 0)) 
    overlay.set_alpha(int((1.0 - brightness) * 255))
    screen.blit(overlay, (0,0))
  


    

    pygame.display.flip()
    clock.tick(60)

pygame.quit()

