
""" FINAL PROJECT"""

# dont forget about language


# Imports
import pygame
import glob
import time
import os
import json
import gettext



#setting up gettext
local_direc = os.path.join(os.path.dirname(__file__), "locale")
current_language = "en"
#setting  up translations
translation = gettext.translation('messages', localedir = local_direc, languages=[current_language], fallback=True)
_ = translation.gettext

# Initialize Pygame
pygame.init()

#music
#music variables
music_on = False
volume = 0.5
settings_selected = 0

pygame.mixer.init()
pygame.mixer.music.load("suspense-gamevideo-track-339286 (1).mp3")
pygame.mixer.music.set_volume(volume)


# Set up the display
SCREEN_WIDTH, SCREEN_HEIGHT = 800, 600
screen = pygame.display.set_mode((SCREEN_WIDTH, SCREEN_HEIGHT))
pygame.display.set_caption("My game")
pygame.mixer.music.play(-1)

clock = pygame.time.Clock()

# Define colors
colors = {
"BLACK": (0, 0, 0),
"WHITE" : (255, 255, 255),
"RED" : (255, 0, 0),
"GREEN" : (0, 255, 0),
"DARK_GREEN" : (0,51,25),
"BLUE" : (0, 0, 255),
"PINK" : (255,192,203),
"YELLOW" : (255,255,51)}
# Variables
script_path = os.path.abspath(__file__)
font = pygame.font.Font(None, 36)
# game states
MENU = 0
GAME = 1
PAUSE = 2
NOT_YET = 3
STORY = 11
LOAD = 4
CREDITS = 5
CONTROLS = 6
HELP = 7
SETTINGS = 8
LANGUAGE = 9
SCENE_1 = 10
INVENTORY = 12
previous_state = None
game_state= MENU   
current_world = "kevin"

#a few saving variables
text_input = None
save_input_active = False

overwrite_confirm = False
overwrite_save_name = ""

delete_confirm = False
delete_save_name = ""

#used to go back to the menu 
back_rect = None

#brightness variables
brightness = 1.0
brightness_step = 0.1
brightness_min = 0.0
brightness_max = 1.0

slider_pos = 400
slider_width = 200
slider_height = 10



# Player class
class Player:
    def __init__(self, x, y, color, size, controls):
        self.x = x
        self.y = y
        self.color = color
        self.width, self.height = size         
        self.controls = controls
        self.is_alive = True
        self.speed = 5
        self.y_velocity = 0 # Tracks falling speed
        self.on_ground = False

# Instances of the player class
kevin = Player(50, 100, colors["YELLOW"], (50, 50), (pygame.K_LEFT, pygame.K_RIGHT, pygame.K_DOWN))
zeth = Player(50, SCREEN_HEIGHT-150, colors["GREEN"], (50, 50), (pygame.K_a, pygame.K_d, pygame.K_w))

class GameObject:
    def __init__(self, x, y, width, height, color,obj_type, image = None):
        self.rect = pygame.Rect(x,y,width,height)
        self.color = color
        self.type = obj_type
        self.collected = False
        self.image = image
    
    def draw(self, screen):
        if not self.collected:
            if self.image:
                screen.blit(self.image, self.rect)
            else:
                pygame.draw.rect(screen,self.color,self.rect)

objects = [
    GameObject(200, 300, 30, 30, colors["RED"], "gun"),
    GameObject(400, 200, 30, 30, colors["BLUE"], "dictionary"),
    GameObject(600, 400, 30, 30, colors["YELLOW"], "key")
]

#platforms - just a placeholder
class Platform:
    def __init__(self,x,y,width,height,color):
        self.rect = pygame.Rect(x,y,width,height)
        self.color = color
    def draw(self,screen):
        pygame.draw.rect(screen,self.color,self.rect)





        

# What I will have on the menu
menu_options = [
                    "NEW",
                    "LOAD",
                    "SETTINGS",
                    "LANGUAGE",
                    "CREDITS",
                    "CONTROLS",
                    "HELP",
                    "EXIT"
                ]
selected_option = 0
options_rects = []


# save function
load_selected = 0
saved_files = []
saved_rects = []

def save_game(save_name):
     if not os.path.exists("saves"):
         os.makedirs("saves")
     file_path = f"saves/{save_name}.json"
     game_data = {
        "kevin_x": kevin.x,
        "kevin_y": kevin.y,
        "zeth_x": zeth.x,
        "zeth_y": zeth.y,
        "current_world": current_world
    }

     with open(file_path, "w") as f:
             json.dump(game_data,f)
     print(f"Saved game to {file_path}") #to make sure it works

def check_overwrite(save_name):
    global overwrite_confirm, overwrite_save_name
    file_path = f"saves/{save_name}.json"

    if os.path.exists(file_path):
        overwrite_confirm = True
        overwrite_save_name = save_name

        return False
    return True

def delete_save():
    global load_selected, saved_files, delete_confirm, delete_save_name

    if saved_files and not delete_confirm:
        delete_confirm = True
        delete_save_name = saved_files[load_selected]
        
    elif saved_files and delete_confirm:
        os.remove(delete_save_name)
        saved_files.pop(load_selected)
        load_selected = max(0, load_selected - 1)
        delete_confirm = False


#type file names
class TextInput:
    def __init__(self,x,y,width,height):
        self.rect = pygame.Rect(x,y,width,height)
        self.text = ""
        self.active = False

    def handle_event(self,event):
        if event.type == pygame.MOUSEBUTTONDOWN:
            self.active = self.rect.collidepoint(event.pos) 

        if self.active and event.type == pygame.KEYDOWN:
            if event.key == pygame.K_RETURN and self.text:
                return self.text   # returning whatever was typed

            elif event.key == pygame.K_BACKSPACE:
                self.text = self.text[:-1] # sapce if needed

            else:
                self.text += event.unicode # letters typed

        return None
    
    def draw(self,screen): # drawing the rects
        pygame.draw.rect(screen,colors["WHITE"],self.rect,2)
        text_surface = font.render(self.text,True,colors["WHITE"])

        screen.blit(text_surface, (self.rect.x + 5, self.rect.y + 5))

#scrolling
scroll_offset = 0
max_display = 8

# languages
languages =  ["English", "Japanese", "Arabic", "Ukrainian"]
language_selected = 0
current_language = "English"

#inventory method
inventory = {"kevin": [], "zeth": []}
def show_inventory():
    screen.fill(colors["BLACK"])
    title = font.render("INVENTORY", True, colors["WHITE"])
    screen.blit(title, (SCREEN_WIDTH//2 - 50, 50))

    y_offset = 100
    for item in inventory[current_world]:
        item_text = font.render(item.type, True, colors["WHITE"])
        screen.blit(item_text, (100, y_offset))

        y_offset += 40

    back_text = font.render("Press I to close", True, colors["WHITE"])
    screen.blit(back_text, (SCREEN_WIDTH//2 - 50, SCREEN_HEIGHT - 50))





RUNNING = True
while RUNNING:
    # Handle events


    pressed_keys = pygame.key.get_pressed()
    mouse_pos = pygame.mouse.get_pos()

    for event in pygame.event.get():
        if event.type == pygame.QUIT:
            RUNNING = False # quit

        if event.type == pygame.KEYDOWN:
            if event.key == pygame.K_ESCAPE and game_state == GAME:
                game_state = PAUSE  # Pause

            elif event.key == pygame.K_r and game_state == PAUSE:
                game_state = GAME

            elif event.key == pygame.K_q and game_state == PAUSE:
                RUNNING = False

            elif event.key == pygame.K_x and game_state == GAME:
                current_world = "kevin"

            elif event.key == pygame.K_y and game_state == GAME:
                current_world = "zeth"

            elif event.key == pygame.K_m and game_state == PAUSE:
                game_state = MENU

            elif event.key == pygame.K_p and game_state == NOT_YET:
                game_state = MENU

            elif event.key == pygame.K_UP and game_state == MENU:
                selected_option = (selected_option - 1) % len(menu_options)

            elif event.key == pygame.K_DOWN and game_state == MENU:
                selected_option = (selected_option + 1) % len(menu_options)

            elif event.key == pygame.K_UP and game_state == LOAD:
                if len(saved_files) > 0:
                    load_selected = (load_selected - 1)% len(saved_files)

                    if load_selected < scroll_offset:
                        scroll_offset = load_selected

                    elif load_selected >= scroll_offset + max_display:
                        scroll_offset = max(0, load_selected - max_display +1)

            elif event.key == pygame.K_DOWN and game_state == LOAD:
                if len(saved_files) > 0:
                    load_selected = (load_selected + 1)% len(saved_files)

                    if load_selected >= scroll_offset+max_display:
                        scroll_offset = load_selected - max_display + 1
                        
                    elif load_selected < scroll_offset:
                        scroll_offset = load_selected


                if load_selected >= scroll_offset + max_display:
                    scroll_offset = min(len(saved_files) - max_display, scroll_offset + 1)

            elif event.key == pygame.K_UP and game_state == LANGUAGE:
                language_selected = max(0, language_selected - 1)
                if language_selected < scroll_offset:
                    scroll_offset = language_selected

            elif event.key == pygame.K_DOWN and game_state == LANGUAGE:
                language_selected = min(len(languages) - 1, language_selected + 1)
                if language_selected >= scroll_offset + max_display:
                    scroll_offset = min(len(languages) - max_display, language_selected)

            elif event.key == pygame.K_RETURN and game_state == LANGUAGE:
                current_language = languages[language_selected]
                lang_map = {"English": "en", "Japanese": "ja", "Arabic": "ar", "Ukrainian": "uk"}
                translation = gettext.translation('messages', localedir=local_direc, languages=[lang_map[current_language]], fallback=True)
                _ = translation.gettext
                game_state = MENU
                    
                    


            elif event.key == pygame.K_UP and  game_state == SETTINGS:
                settings_selected = (settings_selected - 1) % 4

            elif event.key == pygame.K_DOWN and game_state == SETTINGS:
                settings_selected = (settings_selected + 1) % 4

            elif event.key == pygame.K_LEFT and game_state == SETTINGS and settings_selected == 1:
                volume = max(0.0, volume - 0.1)
                pygame.mixer.music.set_volume(volume)

            elif event.key == pygame.K_RIGHT and game_state == SETTINGS and settings_selected == 1:
                volume = min(1.0, volume + 0.1)
                pygame.mixer.music.set_volume(volume)

            elif  game_state == SETTINGS and (event.key == pygame.K_RETURN or event.key == pygame.K_LEFT or event.key == pygame.K_RIGHT) and settings_selected == 0:
                music_on = not music_on
                if music_on:
                    pygame.mixer.music.play(-1)
                else:
                    pygame.mixer.music.stop()
                    
            elif game_state == SETTINGS and settings_selected == 2:
                if event.key == pygame.K_RIGHT:
                    brightness = min(brightness_max, brightness + brightness_step)                
                elif event.key == pygame.K_LEFT:
                  brightness = max(brightness_min, brightness - brightness_step)

            elif game_state == STORY and event.key == pygame.K_RETURN:
                game_state = GAME

            elif event.key == pygame.K_i and game_state == GAME:
                game_state = INVENTORY

            elif event.key == pygame.K_i and game_state == INVENTORY:
                game_state = GAME

            
            elif event.key == pygame.K_d and game_state == LOAD:
                delete_save()

            elif event.key == pygame.K_y and game_state == LOAD and delete_confirm:
                delete_save()

            elif event.key == pygame.K_n and game_state == LOAD and delete_confirm:
                delete_confirm = False

            elif event.key == pygame.K_y and game_state == PAUSE and overwrite_confirm:
                save_game(overwrite_save_name)
                overwrite_confirm = False
                save_input_active = False
                text_input = None

            elif event.key == pygame.K_n and game_state == PAUSE and overwrite_confirm:
                overwrite_confirm = False
                save_input_active = True
                text_input = TextInput(SCREEN_WIDTH // 2 - 100, SCREEN_HEIGHT // 2 + 50, 200, 30)

            elif event.key == pygame.K_RETURN and game_state == LOAD:

                if saved_files:
                    selected_save = saved_files[load_selected]
                    try:
                        with open(selected_save, "r") as f:
                            game_data = json.load(f)
                            kevin.x = game_data["kevin_x"]
                            kevin.y = game_data["kevin_y"]
                            zeth.x = game_data["zeth_x"]
                            zeth.y = game_data["zeth_y"]
                            current_world = game_data["current_world"]
                            game_state = GAME
                    except Exception as e:
                        game_state = NOT_YET
                        
            elif game_state in [LOAD, SETTINGS, LANGUAGE, HELP, CREDITS, CONTROLS ]:
                if event.key == pygame.K_b:
                    game_state = MENU

            elif event.key == pygame.K_RETURN:
                selected = menu_options[selected_option]

                if selected == "NEW":
                    kevin.x, kevin.y = 500, 500  
                    kevin.y_velocity = 0
                    zeth.x, zeth.y = 100, 100    
                    zeth.y_velocity = 0
                    game_state = STORY

                elif selected == "EXIT":
                    RUNNING = False

                elif selected == "LOAD":
                    game_state = LOAD

                elif selected == "CREDITS":
                    game_state = CREDITS

                else:
                    game_state = NOT_YET

            elif event.key == pygame.K_s and game_state == PAUSE:
                save_input_active = True
                text_input = TextInput(SCREEN_WIDTH // 2 - 100, SCREEN_HEIGHT // 2 + 50, 200, 30)


        if save_input_active and text_input:
            result = text_input.handle_event(event)

            if result:
                save_name = result

                if check_overwrite(save_name):
                    save_game(save_name)
                    save_input_active = False
                    text_input = None
        
        if event.type == pygame.MOUSEWHEEL:
            if game_state == LOAD:
                if event.y > 0:  # Scroll up
                    if len(saved_files) > 0:
                        if event.y > 0:
                            load_selected = (load_selected-1)%len(saved_files)

                            if load_selected < scroll_offset:
                                scroll_offset = load_selected

                            elif load_selected >= scroll_offset + max_display:
                                scroll_offset = max(0, load_selected - max_display +1)


                elif event.y < 0:  # Scroll down
                    load_selected = (load_selected + 1) % len(saved_files)

                    if load_selected >= scroll_offset + max_display:
                        scroll_offset = load_selected - max_display + 1

                    elif load_selected < scroll_offset:
                        scroll_offset = load_selected

            elif game_state == LANGUAGE:

                if event.y > 0:  # Scroll up
                        if event.y > 0:
                            language_selected = (language_selected - 1) % len(languages)

                        if language_selected < scroll_offset:
                            scroll_offset = language_selected

                        elif language_selected >= scroll_offset + max_display:
                            scroll_offset = max(0, language_selected - max_display + 1)



                elif event.y < 0:  # Scroll down
                    language_selected = (language_selected + 1) % len(languages)

                    if language_selected >= scroll_offset + max_display:
                        scroll_offset = language_selected - max_display + 1

                    elif language_selected < scroll_offset:
                        scroll_offset = language_selected



        if event.type == pygame.MOUSEBUTTONDOWN and event.button == 1:  # Left-click
                if game_state == MENU:
                    for i, rect in enumerate(options_rects):
                        if rect.collidepoint(event.pos):
                            selected_option = i
                            selected = menu_options[selected_option]
                            if selected == "NEW":
                                kevin.x, kevin.y = 500, 500  
                                kevin.y_velocity = 0
                                zeth.x, zeth.y = 100, 100    
                                zeth.y_velocity = 0
                                game_state = STORY
                            elif selected == "EXIT":
                                RUNNING = False
                            elif selected == "LOAD":
                                game_state = LOAD
                            elif selected == "CREDITS":
                                game_state = CREDITS
                            elif selected == "CONTROLS":
                                game_state = CONTROLS
                            elif selected == "HELP":
                                game_state = HELP
                            elif selected == "SETTINGS":
                                game_state = SETTINGS
                            elif selected == "LANGUAGE":
                                game_state = LANGUAGE
                            else:
                                game_state = NOT_YET
                           
                elif game_state in [LOAD, SETTINGS, LANGUAGE, HELP, CREDITS, CONTROLS]:
                    if back_rect and back_rect.collidepoint(event.pos):
                        game_state = MENU

                elif game_state == STORY:
                    if continue_rect and continue_rect.collidepoint(event.pos):
                        game_state = GAME

                                            
                    elif game_state == LANGUAGE:
                        for i, rect in enumerate(options_rects):
                            if rect.collidepoint(event.pos):
                                language_selected = i + scroll_offset
                                current_language = languages[language_selected]
                                game_state = MENU

                    elif game_state == SETTINGS:
                        for i, rect in enumerate(options_rects):
                            if rect.collidepoint(event.pos):
                                settings_selected = i
                                if i == 0:  # music toggle
                                    music_on = not music_on
                                    if music_on:
                                        pygame.mixer.music.play(-1)
                                    else:
                                        pygame.mixer.music.stop()
                                elif i == 3:  # back button
                                    game_state = MENU
                                    
                        volume_bar_rect = pygame.Rect(SCREEN_WIDTH // 2 + 50, SCREEN_HEIGHT // 2 + 25, 100, 20)
                        if volume_bar_rect.collidepoint(event.pos):
                            click_x = event.pos[0] - (SCREEN_WIDTH // 2 + 50)
                            volume = max(0.0, min(1.0, click_x / 100.0))
                            pygame.mixer.music.set_volume(volume)
                            settings_selected = 1
                            
                        brightness_bar_rect = pygame.Rect(SCREEN_WIDTH // 2 + 50, SCREEN_HEIGHT // 2 + 65, 100, 20)
                        if brightness_bar_rect.collidepoint(event.pos):
                            click_x = event.pos[0] - (SCREEN_WIDTH // 2 + 50)
                            brightness = max(0.0, min(1.0, click_x / 100.0))
                            settings_selected = 2
                                
                    else:
                        for i, rect in enumerate(saved_rects):
                            if rect.collidepoint(event.pos):
                                load_selected = i + scroll_offset
                                selected_save = saved_files[load_selected]
                                try:
                                    with open(selected_save, "r") as f:
                                        game_data = json.load(f)
                                        kevin.x = game_data["kevin_x"]
                                        kevin.y = game_data["kevin_y"]
                                        zeth.x = game_data["zeth_x"]
                                        zeth.y = game_data["zeth_y"]
                                        current_world = game_data["current_world"]
                                        game_state = GAME
                                except Exception as e:
                                    game_state = NOT_YET
                
              


        

# Handling game states
    if game_state == MENU: # menu logic
       



        screen.fill(colors["PINK"])
        title_text = font.render(_("GAME"), True, colors["WHITE"])        
        title_rect = title_text.get_rect(center=(SCREEN_WIDTH // 2, SCREEN_HEIGHT // 8))
        screen.blit(title_text, title_rect)
        options_rects.clear()

        for i, option in enumerate(menu_options):
            text = font.render(_(option), True, colors["BLACK"])  # Temporary color
            rect = text.get_rect(center=(SCREEN_WIDTH // 2, SCREEN_HEIGHT // 4 + i * 40))
            options_rects.append(rect)

        for i, option in enumerate (menu_options):
            hover = options_rects[i].collidepoint(mouse_pos) if i < len(options_rects) else False
            color = colors["RED"] if i == selected_option else colors["BLUE"] if hover else colors["BLACK"]

            text = font.render(_(option), True, color)
            rect = text.get_rect(center=(SCREEN_WIDTH // 2, SCREEN_HEIGHT //4 +i*40))
            screen.blit(text, rect)
            
    elif game_state ==  NOT_YET:# not yet(not implemented) logic
        screen.fill(colors["YELLOW"])
        title_text = font.render(_("NOT YET"), True, colors["WHITE"])        
        title_rect = title_text.get_rect(center=(SCREEN_WIDTH // 2, SCREEN_HEIGHT // 2))
        screen.blit(title_text, title_rect)

        

    elif game_state == LOAD: # load logic
        screen.fill(colors["PINK"])
        title_text = font.render(_("LOAD GAME"), True, colors["WHITE"])        
        title_rect = title_text.get_rect(center=(SCREEN_WIDTH // 2, SCREEN_HEIGHT // 8))

        screen.blit(title_text, title_rect)

        saved_files = glob.glob("saves/*.json")
        saved_rects.clear()

        if not saved_files:
            no_saves_text = font.render(_("No saved games found!"), True, colors["RED"])
            no_saves_rect = no_saves_text.get_rect(center=(SCREEN_WIDTH // 2, SCREEN_HEIGHT // 2))
            screen.blit(no_saves_text, no_saves_rect)

        else:
            saved_files.sort(key=os.path.getmtime, reverse=True)

            for i, saved_file in enumerate(saved_files[scroll_offset:scroll_offset + max_display]):

                file_name = os.path.basename(saved_file)
                mod_time = time.strftime("%Y-%m-%d %H:%M", time.localtime(os.path.getmtime(saved_file)))
                display_text = f"{file_name} ({mod_time})"

                rect = pygame.Rect(SCREEN_WIDTH // 2 - 150, SCREEN_HEIGHT // 4 + i * 40 - 15, 300, 30)
                hover = rect.collidepoint(mouse_pos)
                color = colors["RED"] if i + scroll_offset == load_selected else colors["BLUE"] if hover else colors["BLACK"]
                text = font.render(display_text, True, color)

                saved_rects.append(rect)
                screen.blit(text, text.get_rect(center=(SCREEN_WIDTH // 2, SCREEN_HEIGHT // 4 + i * 40)))

        if delete_confirm:
            confirm_text = font.render(f"{_('Delete')} {os.path.basename(delete_save_name)}? (Y/N)", True, colors["RED"])
            confirm_rect = confirm_text.get_rect(center=(SCREEN_WIDTH // 2, SCREEN_HEIGHT // 2 + 100))
            screen.blit(confirm_text, confirm_rect)

        back_text = font.render(_("Back (B)"), True, colors["BLUE"] if back_rect and back_rect.collidepoint(mouse_pos) else colors["WHITE"])
        back_rect = back_text.get_rect(center=(SCREEN_WIDTH // 2, SCREEN_HEIGHT - 50))
        screen.blit(back_text, back_rect)

    elif game_state == CREDITS: # credits logic
        screen.fill(colors["PINK"])

        title_text = font.render(_("CREDITS"), True, colors["WHITE"])        
        title_rect = title_text.get_rect(center=(SCREEN_WIDTH // 2, SCREEN_HEIGHT // 4))
        
        name_text = font.render(_("Created by Malak Awadallah"), True, colors["WHITE"])
        name_rect = name_text.get_rect(center=(SCREEN_WIDTH // 2, SCREEN_HEIGHT // 2))

        back_text = font.render(_("Back (B)"), True, colors["BLUE"] if back_rect and back_rect.collidepoint(mouse_pos) else colors["WHITE"])
        back_rect = back_text.get_rect(center=(SCREEN_WIDTH // 2, SCREEN_HEIGHT - 50))

        screen.blit(title_text, title_rect)
        screen.blit(name_text, name_rect)
        screen.blit(back_text, back_rect)

    elif game_state == CONTROLS: # controls logic
        screen.fill(colors["PINK"])

        title_text = font.render(_("CONTROLS"), True, colors["WHITE"])
        title_rect = title_text.get_rect(center=(SCREEN_WIDTH // 2, SCREEN_HEIGHT // 4))

        kevin_text = font.render(_("Kevin: Left Arrow (Left), Right Arrow (Right), Down Arrow (Jump)"), True, colors["WHITE"])
        kevin_rect = kevin_text.get_rect(center=(SCREEN_WIDTH // 2, SCREEN_HEIGHT // 2 - 20))

        zeth_text = font.render(_("Zeth: A (Left), D (Right), W (Jump)"), True, colors["WHITE"])
        zeth_rect = zeth_text.get_rect(center=(SCREEN_WIDTH // 2, SCREEN_HEIGHT // 2 + 20))

        back_text = font.render(_("Back (B)"), True, colors["BLUE"] if back_rect and back_rect.collidepoint(mouse_pos) else colors["WHITE"])
        back_rect = back_text.get_rect(center=(SCREEN_WIDTH // 2, SCREEN_HEIGHT - 50))

        screen.blit(title_text, title_rect)
        screen.blit(kevin_text, kevin_rect)
        screen.blit(zeth_text, zeth_rect)
        screen.blit(back_text, back_rect)

    elif game_state == HELP: # help logic
        screen.fill(colors["PINK"])

        title_text = font.render(_("HELP"), True, colors["WHITE"])
        title_rect = title_text.get_rect(center=(SCREEN_WIDTH // 2, SCREEN_HEIGHT // 4))

        help_text1 = font.render(_("Press X (Kevin) or Y (Zeth) to switch worlds."), True, colors["WHITE"])
        help_text1_rect = help_text1.get_rect(center=(SCREEN_WIDTH // 2, SCREEN_HEIGHT // 2))

        back_text = font.render(_("Back (B)"), True, colors["BLUE"] if back_rect and back_rect.collidepoint(mouse_pos) else colors["WHITE"])
        back_rect = back_text.get_rect(center=(SCREEN_WIDTH // 2, SCREEN_HEIGHT - 50))

        screen.blit(title_text, title_rect)
        screen.blit(help_text1, help_text1_rect)
        screen.blit(back_text, back_rect)

    elif game_state == SETTINGS:  # settings logic
        screen.fill(colors["PINK"])

        title_text = font.render(_("SETTINGS"), True, colors["WHITE"])
        title_rect = title_text.get_rect(center=(SCREEN_WIDTH // 2, SCREEN_HEIGHT // 4))
        screen.blit(title_text, title_rect)

        options_rects.clear()

        # Music toggle
        music_text = _("Music: ON") if music_on else _("Music: OFF")
        music_surface = font.render(music_text, True, colors["BLACK"])
        music_rect = music_surface.get_rect(center=(SCREEN_WIDTH // 2, SCREEN_HEIGHT // 2 - 40))
        options_rects.append(music_rect)
        hover = music_rect.collidepoint(mouse_pos)
        color = colors["RED"] if settings_selected == 0 else colors["BLUE"] if hover else colors["BLACK"]
        music_surface = font.render(music_text, True, color)
        screen.blit(music_surface, music_rect)

        # Volume label
        volume_label = font.render(_("Volume"), True, colors["BLACK"])
        volume_label_rect = volume_label.get_rect(center=(SCREEN_WIDTH // 2 - 50, SCREEN_HEIGHT // 2 + 40))
        options_rects.append(volume_label_rect)
        hover = volume_label_rect.collidepoint(mouse_pos)
        color = colors["RED"] if settings_selected == 1 else colors["BLUE"] if hover else colors["BLACK"]
        volume_surface = font.render(_("Volume"), True, color)
        screen.blit(volume_surface, volume_label_rect)

        # Volume bar 
        volume_bar_rect = pygame.Rect(SCREEN_WIDTH // 2 + 50, SCREEN_HEIGHT // 2 + 25, 100, 20)
        hover = volume_bar_rect.collidepoint(mouse_pos)
        bar_color = colors["BLUE"] if not hover else colors["GREEN"]  # Green when hovered
        pygame.draw.rect(screen, colors["BLACK"], volume_bar_rect, 2)
        filled_volume_rect = pygame.Rect(SCREEN_WIDTH // 2 + 50, SCREEN_HEIGHT // 2 + 25, 100 * volume, 20)
        pygame.draw.rect(screen, bar_color, filled_volume_rect)
        
        # Brightness label
        brightness_label = font.render(_("Brightness"), True, colors["BLACK"])
        brightness_label_rect = brightness_label.get_rect(center=(SCREEN_WIDTH // 2 - 50, SCREEN_HEIGHT // 2 + 80))
        options_rects.append(brightness_label_rect)
        hover = brightness_label_rect.collidepoint(mouse_pos)
        color = colors["RED"] if settings_selected == 2 else colors["BLUE"] if hover else colors["BLACK"]
        brightness_surface = font.render(_("Brightness"), True, color)
        screen.blit(brightness_surface, brightness_label_rect)
        
        # Brightness bar
        brightness_bar_rect = pygame.Rect(SCREEN_WIDTH // 2 + 50, SCREEN_HEIGHT // 2 + 65, 100, 20)
        hover = brightness_bar_rect.collidepoint(mouse_pos)
        bar_color = colors["BLUE"] if not hover else colors["GREEN"]
        pygame.draw.rect(screen, colors["BLACK"], brightness_bar_rect, 2)
        filled_brightness_rect = pygame.Rect(SCREEN_WIDTH // 2 + 50, SCREEN_HEIGHT // 2 + 65, 100 * brightness, 20)
        pygame.draw.rect(screen, bar_color, filled_brightness_rect)

        # Back button
        back_text = font.render(_("Back (B)"), True, colors["BLACK"])
        back_rect = back_text.get_rect(center=(SCREEN_WIDTH // 2, SCREEN_HEIGHT - 50))
        options_rects.append(back_rect)
        hover = back_rect.collidepoint(mouse_pos)
        color = colors["BLUE"] if hover else colors["BLACK"]
        back_surface = font.render(_("Back (B)"), True, color)
        screen.blit(back_surface, back_rect)
        
    

    elif game_state == LANGUAGE: # language logic
        screen.fill(colors["PINK"])

        title_text = font.render(_("LANGUAGES"), True, colors["WHITE"])
        title_rect = title_text.get_rect(center=(SCREEN_WIDTH // 2, SCREEN_HEIGHT //4))

        screen.blit(title_text, title_rect)

        options_rects.clear()

        for i, lang in enumerate(languages[scroll_offset:scroll_offset + max_display]):

            text = font.render(lang, True, colors["BLACK"])
            rect = text.get_rect(center=(SCREEN_WIDTH // 2, SCREEN_HEIGHT // 3 + i * 45))
            options_rects.append(rect)

        for i, lang in enumerate(languages[scroll_offset:scroll_offset + max_display]):

            hover = options_rects[i].collidepoint(mouse_pos)
            color = colors["RED"] if i + scroll_offset == language_selected else colors["BLUE"] if hover else colors["BLACK"]
            text = font.render(lang, True, color)
            screen.blit(text, options_rects[i])

        back_text = font.render(_("Back (B)"), True, colors["BLUE"] if back_rect and back_rect.collidepoint(mouse_pos) else colors["WHITE"])
        back_rect = back_text.get_rect(center=(SCREEN_WIDTH // 2, SCREEN_HEIGHT - 50))

        screen.blit(back_text, back_rect)

    elif game_state == STORY:
        pygame.mixer.music.stop()

        screen.fill(colors["BLACK"])

        story_text_placeholder = font.render(_("My STORY"), True, colors["WHITE"])
        story_rect = story_text_placeholder.get_rect(center=(SCREEN_WIDTH // 2, SCREEN_HEIGHT //4))
        screen.blit(story_text_placeholder, story_rect)

        mouse_pos = pygame.mouse.get_pos()
        continue_rect = pygame.Rect(0, 0, 200, 40) 
        continue_rect.center = (SCREEN_WIDTH//2, SCREEN_HEIGHT-50)
        hover = continue_rect.collidepoint(mouse_pos)
        color = colors["BLUE"] if hover else colors["WHITE"]
        continue_text =  font.render(_("CONTINUE --->"), True, color)
        text_rect = continue_text.get_rect(center=continue_rect.center)
        screen.blit(continue_text, text_rect)
        

    


    elif game_state == GAME: # game play logic
       
        if pygame.mixer.music.get_busy():
           pygame.mixer.music.stop()
        elif game_state == MENU:
           if not pygame.mixer.music.get_busy():
               pygame.mixer.music.play(-1)

        #camera offset for scrolling
        if current_world == "kevin":
            camera_offset = -kevin.x + SCREEN_WIDTH // 4  # Keep player at 1/4 of screen
           
        else:
            camera_offset = -zeth.x + SCREEN_WIDTH // 4
            
        
      
        if current_world == "kevin":
            screen.fill(colors["PINK"])
            pygame.draw.rect(screen, kevin.color, (kevin.x + camera_offset, kevin.y, kevin.width, kevin.height))

        elif current_world == "zeth":
            screen.fill(colors["DARK_GREEN"])
            pygame.draw.rect(screen, zeth.color, (zeth.x + camera_offset, zeth.y, zeth.width, zeth.height))

        player_rect = pygame.Rect(kevin.x, kevin.y, kevin.width, kevin.height) if current_world == "kevin" else pygame.Rect(zeth.x, zeth.y, zeth.width, zeth.height)
        for obj in objects:
            if not obj.collected and player_rect.colliderect(obj.rect):
                obj.collected = True
                inventory[current_world].append(obj)
                print(f"{current_world} collected {obj.type}")

        if current_world == "kevin":

            if pressed_keys[kevin.controls[0]] and kevin.x > 0:
                kevin.x -= kevin.speed # Left
                print(kevin.x)

            if pressed_keys[kevin.controls[1]]:  
                kevin.x += kevin.speed # Right

            if pressed_keys[kevin.controls[2]] and kevin.on_ground:
                kevin.y_velocity = 10
                kevin.on_ground = False

        elif current_world == "zeth":

            if pressed_keys[zeth.controls[0]] and zeth.x > 0:
                zeth.x -= zeth.speed # Left

            if pressed_keys[zeth.controls[1]]:  
                zeth.x += zeth.speed  # Right

            if pressed_keys[zeth.controls[2]] and zeth.on_ground:
                zeth.y_velocity = -10
                zeth.on_ground = False

        kevin_rect = pygame.Rect(kevin.x, kevin.y, kevin.width, kevin.height)
        zeth_rect = pygame.Rect(zeth.x, zeth.y, zeth.width, zeth.height)

        kevin.on_ground = False
        zeth.on_ground = False
        
   
        kevin.y_velocity -= 0.5
        kevin.y += kevin.y_velocity
        kevin.on_ground = False

        if kevin.y < 0:
            kevin.y = 0
            kevin.y_velocity = 0
            kevin.on_ground = True

        zeth.y_velocity += 0.5
        zeth.y += zeth.y_velocity
        zeth.on_ground = False

        if zeth.y > SCREEN_HEIGHT - zeth.height:
            zeth.y = SCREEN_HEIGHT - zeth.height
            zeth.y_velocity = 0
            zeth.on_ground = True

    elif game_state == INVENTORY:
        show_inventory()


    elif game_state == PAUSE:  # pausing logic
        pressed_keys = pygame.key.get_pressed()
        for event in pygame.event.get():
                if event.type == pygame.QUIT:
                    break


        title_text = font.render(_("YOU PAUSED"), True, colors["WHITE"])
        title_rect = title_text.get_rect(center=(SCREEN_WIDTH // 2, SCREEN_HEIGHT // 4))
        screen.blit(title_text, title_rect)

        text = font.render(_("Paused: R to Resume, Q to Quit, S to Save, M to go back to Menu"), True, colors["WHITE"])
        text_rect = text.get_rect(center=(SCREEN_WIDTH // 2, SCREEN_HEIGHT // 2))
        screen.blit(text, text_rect)

        if save_input_active and text_input:
            text_input.draw(screen)
        if overwrite_confirm:
            confirm_text = font.render(f"{_('Overwrite')} {overwrite_save_name}.json? (Y/N)", True, colors["RED"])
            confirm_rect = confirm_text.get_rect(center=(SCREEN_WIDTH // 2, SCREEN_HEIGHT // 2 + 100))
            screen.blit(confirm_text, confirm_rect)
                    
                    
    # Overlay
    overlay = pygame.Surface((SCREEN_WIDTH, SCREEN_HEIGHT))
    overlay.fill((0, 0, 0)) 
    overlay.set_alpha(int((1.0 - brightness) * 255))
    screen.blit(overlay, (0,0))
  


    

    pygame.display.flip()
    clock.tick(60)

pygame.quit()

